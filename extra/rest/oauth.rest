### oauth get user info
GET http://www.test:8080/provider/userinfo HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.Av5GeRYCWoQ5is6r6U9GLBBFW-wxaiLXkjPa0ijWYus
# cookie: apsv=appDebug;rft=OYaPXD--awOlg7lw_NxAP.1764711554506;

{
}

### oauth get jwt/rft
POST http://host.docker.internal:8099/auth/convertjwt HTTP/1.1
Content-Type: application/json
# Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.Av5GeRYCWoQ5is6r6U9GLBBFW-wxaiLXkjPa0ijWYus
# cookie: apsv=appDebug;rft=OYaPXD--awOlg7lw_NxAP.1764711554506;

{
  wk:'aQtVVtLH_4EG1qZ-BZzgv.1766045718430'
}