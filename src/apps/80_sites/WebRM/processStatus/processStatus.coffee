ProcessStatusModel = MODEL 'ProcessStatus'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
{respError} = INCLUDE 'libapp.responseHelper'

APP 'proccess'
# apis
# TODO: use separate k3s pod for this, use standard auth
POST (req,resp)->
  try
    #check header secrect
    # debug.info req.headers
    # NOTE: headers = {'secret':'S-qq8vm'} or {secret:'S-qq8vm'}, should use standard auth
    secret = req.headers?['secret'] or req.headers?.secret
    if secret isnt 'S-qq8vm'
      return respError {clientMsg:MSG_STRINGS.ACCESS_DENIED, resp}
    status = await ProcessStatusModel.checkStatus()
    return resp.send {ok:1, status}
  catch err
    return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp:resp}
