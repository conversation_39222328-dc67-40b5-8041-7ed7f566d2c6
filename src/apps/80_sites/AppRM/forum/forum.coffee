config = CONFIG(['share','serverBase'])
gShareHostNameCn = config.share?.hostNameCn
gShareHost = config.share.host
Cache = INCLUDE 'lib.cache'
helpers = INCLUDE 'lib.helpers'
libproperties = INCLUDE 'libapp.properties'
wordpressHelper = INCLUDE 'libapp.wordpressHelper'
i18n = INCLUDE 'lib.i18n'
libUser = INCLUDE 'libapp.user'
{getShareDesc,getShareImg} = INCLUDE 'libapp.forum'
{ replaceRM2REImagePath, uploadPathRegex } = INCLUDE 'libapp.propertyImage'

BlockPhoneModel = MODEL 'BlockPhone'
ForumCol = COLLECTION  'chome','forum'
User = COLLECTION 'chome', 'user'#需要用到obiectid
# GroupForumCol = COLLECTION 'chome','group_forum'
SysData = COLLECTION 'chome','sysdata'
News = COLLECTION 'chome','news'
Wecard = COLLECTION 'chome','wecard'
UserProfile = COLLECTION 'chome','user_profile'
# UserListings = COLLECTION 'chome','user_listing'
GroupModel = MODEL 'Group'

Properties = MODEL 'Properties'
ForumModel = MODEL 'Forum'
UserModel = MODEL 'User'
TaskModel = MODEL 'Task'
PromptsModel = MODEL 'Prompts'

getConfig = DEF 'getConfig'
# accessAllowed = DEF '_access_allowed'
dataMethods = DEF 'dataMethods'
SysNotifyModel = MODEL 'SysNotify'
getWXConfig = DEF 'getWXConfig'
allowed_category = DEF 'allowed_category'
allowed_sub = DEF 'allowed_sub'
{respError} = INCLUDE 'libapp.responseHelper'

# addWpComment = wordpressHelper.addWpComment
# deleteWpComment = wordpressHelper.deleteWpComment
deleteFromWp = wordpressHelper.deleteFromWp
dataMethodCall = DEF 'dataMethodCall'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
ProvAndCity = MODEL 'ProvAndCity'
libAppVer = INCLUDE 'libapp.appVer'

wordpressHelper.setUpCollection {UserProfile:UserProfile, Forum: ForumCol, SysData: SysData}

isGroupAdmin = DEF 'isGroupAdmin'
# dataMethods.forumCityList = forumCityList = (req, user, cb) ->
#   SysData.findOne {_id: 'forum_city_list'},(err,data)->
#     cb(err, data?.city)

getMyRating = DEF 'getMyRating'
# setCmntThumbUpAndRating = DEF 'setCmntThumbUpAndRating'
processCmnts = DEF 'processCmnts'
checkTextContent = DEF 'checkTextContent'

getAllIds = (objFld,proj)->
  ret = {}
  return ret unless Object.keys(objFld) and proj
  for fld in Object.keys(objFld)
    continue unless proj[fld]
    ret[fld]=proj[fld]
  return ret

injectPropDetail = (opt, fpost, cb)->
  if not id = (fpost.pid or fpost.sid)
    return cb null,fpost
  fields = libproperties.LIST_PROP_FIELDS
  # q = {sid:fpost.sid}
  # if fpost.pid
  #   q = {_id:fpost.pid}
  # Properties.findOne q,{fields:fields},(err,prop)->
  Properties.findOneByFuzzID id, {projection:fields},(err,prop)->
    return cb(null, fpost) if err or (not prop)
    # prop.ptype2_en = prop.ptype2.slice()
    prop.ptype2 = opt.l10n prop.ptype2
    prop.city = opt.l10n prop.city
    #TODO: isPad ？？
    cfg = {num:1,useThumb:1,use3rdPic:config.serverBase?.use3rdPic,shareHostNameCn:config.share?.hostNameCn}
    libproperties.genListingPicReplaceUrls(prop,opt.isCip,cfg)
    prop.img = prop.thumbUrl
    fpost.prop = prop
    return cb null,fpost

getDispVar = (req,user, cb)->
  dispVar = {}
  datas = ['isCip','isApp','userCity','isLoggedIn','lang','forumAdmin','sessionUser','shareHost',\
    'canWebComment','globalTags','isVipRealtor','isAdmin','isRealtor','edmAdmin','userGroups']
  if req.param('aid')
    req.body = req.body  or  {}
    req.body.ownerId = req.param('aid')
    datas.push('ownerData')
  dataMethodCall req, user, datas, cb
DEF 'getDispVar',getDispVar
# updateForumHist =(uid, postid, cb) ->
#   return cb() unless uid and postid
#   update =
#     $set: pnEdmMt:new Date()
#     $push:
#       forumhist:
#         $each:[{id: postid,ts: new Date()}]
#         $slice: -100
#   pull = {$pull: {forumhist: {id: postid}}}
#   UserProfile.updateOne {_id:uid}, pull,(err)->
#     console.log err.toString() if err
#     return cb(err) if err
#     UserProfile.updateOne {_id:uid}, update, {upsert: true},(err)->
#       console.log err.toString() if err
#       return cb()
#
# DEF 'updateForumHist',updateForumHist

genMlsImgPath = (ml_num, nth)->
  n = parseInt(nth) or 1
  if n is 1
    return '/'+n+'/'+ml_num.substr(-3)+'/'+ml_num+'.jpg'
  else
    return '/'+n+'/'+ml_num.substr(-3)+'/'+ml_num+'_'+n+'.jpg'

#given p, return first image
parseUserListingImage = (p={})->
  pic = p.pic
  l = pic?.l
  unless pic and l and l[0]?
    return '/img/noPic.png'
  mlbase = pic.mlbase
  img = l[0]
  if img[0] is '/'
    # http://img.realmaster.com/mls /1/813/W3565813.jpg
    # http://img.realmaster.com/mls /2/813/W3565813_2.jpg
    return mlbase+genMlsImgPath(p.sid, img.substr(1))
  else
    # http://f.i.realmaster.com/T/F /R.jpg
    if /^http/.test img
      return img
    base = pic.base
    fldr = pic.fldr
    # if base.split('/').length
    return base+'/'+img
    # else
    #   return base+'/'+fldr+'/'+img

#parse User listing format to wecard format
parseUserListingList = (l)->
  ret = []
  for p in l
    tmp = { meta:{} }
    tmp.meta.title = p.tl or ('RealMaster '+p.lp+' '+p.addr+', '+p.city)
    tmp.meta.desc = (p.m or '').substr(0,100)
    tmp.meta.img = parseUserListingImage(p)
    tmp.meta.prop = true
    tmp.meta.id = p.id
    ret.push tmp
  ret

getRcommendCardList = (uid, cb)->
  fields = {
    _id:1,
    'meta.title':1,
    'meta.desc':1,
    'meta.img':1
  }
  # TODO: move to modal
  Wecard.findToArray {uid:uid , rcmd:1}, {fields:fields}, {limit:30}, (err, cards)->
    return cb err if err
    cards ?= []
    for c in cards
      if uploadPathRegex.test c?.meta?.img
        c.meta.img = replaceRM2REImagePath(c.meta.img)
    Properties.findUserRcmdListings uid,(err,plist)->
      plist ?= []
      cards = parseUserListingList(plist).concat(cards)
      cb null, cards

# isGroupAdmin=(req, user, gid, cb)->
#   return cb(false) unless gid
#   req.userGroups user, (err, groups)->
#     return cb(false) unless gid and groups
#     grp = groups.find((g) ->
#       return g._id.toString() is gid;
#     )
#     if grp  and  (grp.isAdmin or grp.isOwner)
#       return cb(true)
#     else
#       return cb(false)

#TODO: may need adjust time on china server?
#calc whether if ohv is within time period
calcIsLiving = (ohv)->
  now = new Date()
  l = ohv.split(' ')
  date = l[0]
  ts = l[1].split('-')
  d1 = new Date(date+' '+ts[0])
  d2 = new Date(date+' '+ts[1])
  # console.log('++++++',d1,now,d2)
  [now >= d1  and  now < d2, now > d2]

#2020-03-17 2:00PM
formatLiveTime = (ohv='')->
  return '' unless ohv
  l = ohv.split(' ')
  ts = l[1].split('-')
  start = ts[0].split(':')[0]
  text = 'AM'
  if parseInt(start)>=12
    text = 'PM'
  return l[0]+' '+ts[0]+text
getForumList = (req, params,user, cb)->
  params.isForumAdmin = UserModel.accessAllowed('forumAdmin',user)
  params.hasRoleRealtor = req.hasRole('realtor')
  params.locale = req.locale()
  params.user = user
  if params.category is 'my_draft'
    try
      forums = await ForumModel.findAllDraftList params
    catch err
      debug.error err
      return cb err
    return cb null, forums
  req.userGroups user, (err, groups)->
    gids = groups?.map((g)->
      return g._id
    )
    params.gids = gids
    ForumModel.findList params,(err,forums)->
      if err
        console.log err
        return cb err
      if (not user) or (not forums?.length) or (params.category is 'my_group') or params.gid
        return cb(null,forums)
      else
        UserModel.getForumHistories user._id, (err, forumLog) ->
          # linerize calculation. Avoid Loop in loop
          forumhistObj = {}
          # if forumhist = profile?.forumhist
          #forumLog是ts倒序排序的。
          for i in [(forumLog?.length - 1)..0]
            if hist = forumLog[i]
              if hist.tss and Array.isArray(hist.tss)
                forumhistObj[hist.id.toString()] = hist.tss[hist.tss.length - 1]
              else
                forumhistObj[hist.id.toString()] = hist.ts
          for forum in forums
            if forum?.ohv
              state = calcIsLiving(forum.ohv)
              forum.isLiving = state[0]
              forum.passedLive = state[1]
            if params.category is 'topic'
              forum.m = forum.m?.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
              forum.m = forum.m?.replace(/(<([^>]+)>)/ig, '')
              forum.m = forum.m?.substr(0,100)
            unless req.hasRole '_admin'
              forum.vc+= forum.vcc if forum.vcc
              delete forum.vcc
              delete forum.vcapp
              delete forum.vcd
            if (cmnts = forum.cmnts) and Array.isArray(cmnts)
              lastCmntUser = cmnts.slice(-1)
              lastCmntUserId = lastCmntUser[0].uid
            # last opened ts < comment ts 并且 最后一个cmnt不是本人发出
            if (fTs = forumhistObj[forum._id.toString()]) and (new Date(fTs) < new Date(forum.cmt)) and (lastCmntUserId.toString() isnt user._id.toString())
              forum.hasUpdate = true
          return cb null, forums if not gids?.length or params.yellowpage
          params.isGroupForum = 1
          ForumModel.findList params,(err,ret)->
            if err
              console.log err
              return cb err
            forums= ret.concat forums
            return cb null, forums


DEF 'getForumList', getForumList
DEF 'getRcommendCardList', getRcommendCardList
DEF 'parseUserListingList', parseUserListingList
# TODO: find spuser inject
# TODO: handle when del is true
getForumDetail = (req,resp, id, cb) ->
  error = (err) ->
    console.log err
    return cb(req.l10n(MSG_STRINGS.DB_ERROR))
  unless id and 'string' is typeof id
    return cb req.l10n(MSG_STRINGS.BAD_PARAMETER)

  isedit = req.param('isedit')
  type = req.param('type')
  gid = req.param('gid')
  locale = req.locale()

  ForumModel.findDetail {isedit,locale,id,type,gid},(err,post)->
    if err
      return error(err)
    else if !post
      return cb(null,null)
    post.m = post.m?.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
    # 同样处理原文内容的HTML转义
    post.origM = post.origM?.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
    post.origTl = post.origTl?.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
    if post.thumb and (post.thumb not in ['undefined','null']) and !post.photos?.length
      post.photos = [post.thumb]
    if post.src in ['property'] #video
      # 'http://www.realmaster.cn/img/no-pic.png'
      post.photos=["#{gShareHost}/img/no-pic.png"] unless post.photos?.length
    if !isedit
      obj = helpers.pictureReplace(post.m, post.photos)
      origMObj = helpers.pictureReplace(post.origM, post.photos)
      post.m = obj.txtDone
      post.photos = obj.picLeft
      post.origM = origMObj.txtDone
    post.cmnts = post.cmnts or []
    post.youtubeID = extractID(post.vidUrl) if post.vidUrl
    getMyRating post, req
    processCmnts post, req
    # for cmnt, idx in post.cmnts
    #   cmnt.sticky = cmnt.sticky  or  false
    #   cmnt.del = cmnt.del  or  false
    #   setCmntThumbUpAndRating post.rating, req.user, cmnt, idx
    UserModel.appAuth {req,resp}, (_user)->
      post.vc ?= 0 # in case this is the first time
      post.vc++ # count this time
      if post.readers and _user
        el = post.readers.find((reader)->
          return reader.uid.toString()  is  _user._id.toString()
          )
        if el
          post.readStatus = el.status
      return cb(null, {ok:1, post:post}) if type  is  'summary'
      unless UserModel.accessAllowed 'admin',_user
        post.vc += (post.vcc or 0)
        delete post.vcc
      opt = {
        devType:'app'#for spagent/spwebagent
        isAllowedMarketAdmin:req.isAllowed('marketAdmin')
        locale:req.locale()
      }
      opt2 = {
        isCip:req.isChinaIP(),
        l10n:(a,b)->req.l10n(a,b),
        isPad:req.isPad()
      }
      injectPropDetail opt2,post,(err,post)->
        return error err if err
        try
          post = await UserModel.injectSponsorUser opt,post
        catch err
          debug.error err
          return error MSG_STRINGS.DB_ERROR
        findRelatedPosts post, req, resp, (err, relatedposts)->
          return error err if err
          return cb(null, {ok:1, post:post, relatedPosts:relatedposts}) unless post.uid
          UserModel.findById post.uid.toString(),{},(err, user) ->
            return error err if err
            authorIsVip = UserModel.accessAllowed 'vipUser', user
            post.isMerchant = true if req.hasRole 'merchant',user
            cb(null, {ok:1, post:post, relatedPosts:relatedposts, authorIsVip: authorIsVip})
            #do update counter and user profile after
            devType = req.getDevType()
            forumHotThreshold = getConfig('forumHotThreshold')
            ForumModel.incViewCount {id,gid,post,devType,forumHotThreshold}, (err,ret)->
              if _user
                UserModel.logForumHistory _user._id, {postid:post._id}

DEF 'getForumDetail', getForumDetail

VIEW 'forum-detail-new',->
  css '/css/apps/forumDetail.css'

  coffeejs {}, ->
    setUpPreview = (el) ->
      w = el.naturalWidth
      h = el.naturalHeight
      if w>0 and h>0
        el.setAttribute('data-size', w + 'x' + h)

    ###*
     * 切换主帖原文显示
     ###
    window.togglePostOriginal =(btn) ->
      post = window.vars?.post
      return unless post?.origTl and post?.origM

      titleElement = document.getElementById('postTitleText')
      contentElement = document.getElementById('postContentContainer')
      viewOriginalElement = document.querySelector('.viewOriginal')
      closeOriginalElement = document.querySelector('.closeOriginal')

      return unless titleElement and contentElement
      classie.toggle viewOriginalElement, 'displayNone'
      classie.toggle closeOriginalElement, 'displayNone'
      isShowingOriginal = btn is 'view'

      if isShowingOriginal
        # 显示原文内容
        titleElement.textContent = post.origTl
        contentElement.innerHTML = '<div>' + post.origM + '</div>'
      else
        # 显示翻译后的内容
        titleElement.textContent = post.tl
        contentElement.innerHTML = '<div>' + post.m + '</div>'

    null

  div ->
    div class:'WSBridge', style:'display:none',->
      span id: 'share-title-en', ->
        text @post.tl
      span id: 'share-desc-en',->
        text @post.shareDesc
      span id: 'share-title', ->
        text @post.tl
      span id: 'share-desc',->
        text @post.shareDesc
      span id: 'share-image',->
        text @post.shareImage
    if (not @inFrame) and @post.src isnt 'video' and not @fromForumList
      header class:'bar bar-nav', ->
        if @iswebAdmin
          a class:'icon fa fa-back pull-left', href:'/1.5/forum', ->
        div class:'title',->
          _('RealMaster')
    if @post.gid and  (@post.readStatus isnt 'read') and  @inFrame
      div class:'pull-right read-status',->
        span class:'btn btn-primary', onclick:"selectUnread('later')",style:'top:2px',->
          _('Unread','forum')
    # returns 'video','waiting'
    getVideoState = (post)->
      if post.vidLive
        if post.isLiving or post.passedLive
          return 'video'
      if post.vidRecord
        return 'video'
      'waiting'
    state = getVideoState(@post)
    if state is 'video' and @post.youtubeID and @post.src is 'video'
      div class:'video-wrapper parent',->
        # &autoplay=1&mute=1 633b3e91e7b20d81d55903f3387f52fb297bab2b
        # NOTE: single quote wont handle #{} in string
        text """<iframe class="video-wrapper" data-src="realmaster" style="min-height:200px" width="375" height="211" src="https://www.youtube.com/embed/#{@post.youtubeID}?playsinline=1&rel=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; " sandbox="allow-same-origin allow-presentation allow-scripts allow-forms allow-modals" allowfullscreen></iframe>"""
    if state is 'waiting' and @post.src is 'video'
      div class:'video-wrapper', style:'width:400px; height:300px; position:fixed; top: 0;overflow: hidden;z-index: 10; box-shadow: 0px 2px 11px 0px #27272742;',->
        src = @post.photos[0] or '/img/black.png'
        img src:src,style:'width:100%;height:100%', referrerpolicy:"same-origin"
        div class:'time',->
          div class:'left',->
            i class:'fa fa-feed'
          div class:'right',->
            div -> text _ 'Live at'
            div -> text _ @post.liveTime
    div class:'content container', ->
      div id:'postViewContainer',->
        if @post.src is 'video'#state is 'video'
          ratio = @post.ratio or '16:9'
          l = ratio.split(':')
          percent = parseInt(l[1])/parseInt(l[0])
          text "<script>
          var wrapper = document.querySelectorAll('.video-wrapper');
          var width = window.innerWidth+'px';
          var height = window.innerWidth*#{percent}+'px';
          if(wrapper.length){
            var index = 0, length = wrapper.length;
            for ( ; index < length; index++) {
              wrapper[index].height = height;
              wrapper[index].width = width;
              wrapper[index].style.width = width;
              wrapper[index].style.height = height;
            }
          }
          var wrapper2 = document.querySelector('#postViewContainer');
          if(wrapper2){
            wrapper2.style.marginTop = window.innerWidth*#{percent}+'px';
          }
          </script>"
        if @post.src is 'video'
          div id:'videoTitle',-> text @post.tl
        showSplitLines = false
        if (@post.src is 'video') and (@post.agents?.length or ((@post.pid or @post.sid)  and  @post.prop))
          showSplitLines = true
        if showSplitLines
          div class:'splitDiv',->
        if (@post.src is 'video') and @post.agents?.length
          agent = @post.agents[0]
          div id:'spagent', class:'post-author', onclick:"showAgentInfo('#{agent._id}')", ->
            img id:'authorAvt', class:'img post-img', src: agent.avt or '/img/user-icon-placeholder.png', referrerpolicy:"same-origin", ->
            div style:'float:left; margin-top: -5px',->
              span class:'name', ->
                text agent.fnm
              # div class:'ts',->
              #   text agent.cpny
        if (@post.src is 'video') and (@post.pid or @post.sid) and @post.prop
          div id:'forumProp', onclick:"goToProperty('#{@post.prop._id}')",->
            text '<prop-info></prop-info>'
        if showSplitLines
          div class:'splitDiv',->
        else if @post.src  is  'video'
          div style:'height:15px;'
        if @post.adTopPhoto
          div class: 'post-top-container',->
            img src: @post.adTopPhoto, onclick:'openAd()',->
            div class:'post-top-text',->
              text  _('AD','forum')
              if @isForumAdmin
                span @post.vcad0
        else if not @post.noCpm and @cpm._id
          text ckup 'cpmBanner',{cpm:@cpm}
        if @post.realtorOnly
          span class:'realtor-only',->
            text _('Realtor Only','forum')
        if @post.gid  and  @post.gnm
          span class:'realtor-only', style:'font-size:14px; padding: 2px 15px', ->
            @post.gnm
        if @post.src isnt 'news'
          # TODO: @julie, cpm ad only banner type
          if @post.src isnt 'video'
            h3 id:'postTitle', class:'post-title',->
              if @post.src is 'property'
                span id:'propertyTitle', onclick:'goToProperty()',->
                  if @post.cmntl
                    text  @post.cmntl+' '+ (@post.tl or '')
                  else
                    text @post.tl
              else
                span id:'postTitleText',->
                  text @post.tl
            # 主帖原文显示功能
            if @post.origTl or @post.origM
              div id:'postOriginalToggle', style:'margin-top: 5px;',->
                a href:'javascript:void(0)', style:'color: #7ebae5; text-decoration: none; font-size: 14px;',->
                  span class:'viewOriginal', onclick:'togglePostOriginal("view")',->
                    text _('View original')
                  span class:'closeOriginal displayNone', onclick:'togglePostOriginal("close")',->
                    text _('Close original')
          # if (@post.src  is  'video') and @post.m?.length
          #   div id:"moreDetailBtn", onclick:"showMoreDetail()",->
          #     text _ 'More Detail'
          if @post.fornm and @post.src is not 'video'
            div class:'post-author',->
              img id:'authorAvt', class:'img post-img',onclick:'showAuthorInfo()', src: @post.avt or '/img/user-icon-placeholder.png', ->
              div style:'float:left; margin-top: -5px',->
                span class:'name',->
                  text @post.fornm
                div id:'forumTs', class:'ts',->
          if @post.m
            if (@post.src is 'post') and (not @post.wpHosts)
              pre ->
                div id:'postContentContainer', class:'post-content', ->
                  text '<div>'
                  text @post.m
                  text '</div>'
            else
              pclass = 'post-content'
              # if (@post.src  is  'video') and @post.m?.length
              #   pclass += ' hide'
              div id:'postContentContainer', class:pclass, ->
                text '<div>'
                text @post.m
                text '</div> '

        else if @post.src is 'news'
          div class:'post-content', ->
            text '<div>'
            text @post.m or @post.tl
            text '</div> '

        # if @post.photos and @post.photos.length and @post.src!='news' and not @post.wpHosts
        #   div class:"post-photo",->
        #     for photo in @post.photos
        #       div class:"img-wrapper",->
        #         img src: photo, onclick:"previewPic(event)",onload:"setUpPreview(this)",onerror:"setDefaultPhoto(this)",->
        #         if @post.src is "property"  and  @post.status != "new"
        #           span class:"search", onclick:"goToProperty()", ->
        #             span class:"details",->
        #               text _('Details','forum')
        #             span class:"fa fa-search-plus"
    # 如果 @fromForumList 为 true，添加 'container' class，否则不变
    div id:'forumDetail', class: "#{if @fromForumList then 'container' else ''}", ->
      text '<app-forum-detail-content></app-forum-detail-content>'
    # no footer need if share.
    if not @share and not @fromForumList
      div id:'forumDetailFooter',->
        text '<app-forum-detail-footer></app-forum-detail-footer>'
  coffeejs {
    vars:{
      post:Object.assign {}, @post, {m:@post.m, origTl:@post.origTl, origM:@post.origM}
      relatedPosts: @relatedPosts
      authorIsVip: @authorIsVip
      from: @from
      dispVar: @dispVar
      loc: 'NB1'
      cpm: @cpm
      prov: @prov
      city: @city
      isApp:@isApp
      fromForumList: @fromForumList
      }
    }, ->
      null
  js '/js/classie.js'
  js '/js/forum_detail_new.min.js'
  if not @isApp #is app dealed in forum_detail_new.coffee
    js '/js/entry/commons.js'
    js '/js/entry/forumDetail.js'


APP '1.5'
APP 'topics', true
GET 'details',(req,resp) ->
  getForumDetailPage2 req, resp

APP '1.5'
APP 'forum', true

POST 'getBlockUsers',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    isAdmin = UserModel.accessAllowed 'forumAdmin', user
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless isAdmin
    selectedUID = req.body.selectedUID
    UserModel.getForumBlockList selectedUID,(err,ret)->
      ret?= []
      resp.send {ok:1,blockUsers:ret}

POST 'blockUser',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    isAdmin = UserModel.accessAllowed 'forumAdmin', user
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless isAdmin
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless uid = req.body.uid
    forBlk = req.body.forBlk
    UserModel.setForumBlockById uid, forBlk, (err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      resp.send {ok:1}

POST 'changeStatus', (req, resp)->
  id = req.body.id
  status = req.body.status
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id and status

  UserModel.appAuth {req,resp,userfields:['nm']}, (user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
    ForumModel.findGroupForumReaders id,(err,forum)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp} unless forum
      ForumModel.changeGroupForumStatus {id,user,forum,status},(err)->
        console.log err.toString() if err
        resp.send {ok:1}

POST 'adClick',(req,resp) ->
  {id,index,gid} = req.body
  if not (id and ('number' is typeof index))
    #TODO:@rain.forBlk : true is added to the returned error.julie 2021-05-27
    #{"e":"Bad Parameter","ok":0,"forBlk":true}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  ForumModel.incAdClick {id,index,gid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1}

POST 'edm',(req, resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.BAD_PARAMETER ,resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless req.isAllowed 'edmAdmin'
    _id = req.param '_id'
    return respError {clientMsg:'Need _id',resp} unless _id
    # always check parameters from input for security measure.
    {type,del,gid} = params
    return respError {clientMsg:'Invalid type',resp} unless ['edm','edmw','edmd'].indexOf(type) >= 0
    ForumModel.updateEdmStatus {id:_id,type,del,gid},(err,ret)->
      console.log err if err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return resp.send {ok:1} if del
      obj = {ok:1}
      obj[type] = ret.edmts if ret
      return resp.send obj

POST 'updateForumhist', (req,resp) ->
  #forum id is not always objectID, can be string from property.
  id = req.body?.id
  unless ('string' is typeof id)
    console.log "Bad forum id #{id}"
    return resp.send {err:'Bad ID'}
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:1} unless user
    UserModel.logForumHistory user._id, {postid:id}
    return resp.send {ok:1}

GET 'authorInfo',(req,resp)->
  uid = req.param('uid')
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless uid
  UserModel.findPublicInfo {lang:req.locale(), id:uid}, (err, user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1, user:user}


editAuthCheck = (req,resp, url) ->
  UserModel.appAuth {req,resp}, (user)->
    maxImageSize = getConfig('maxImageSize')
    return resp.redirect 'http://'+req.host+'/www/login' if (req.param 'web') and !user
    return resp.redirect '/1.5/user/login' unless user
    resp.noCache()
    return resp.ckup url,{maxImageSize},'_',{noAngular:true}

gForumNotifyObj={}

setNotifyObj = ({status,postid})->
  switch status
    when 'publishing'
      gForumNotifyObj[postid] = status
    when 'err'
      delete gForumNotifyObj[postid]
    else
      gForumNotifyObj[postid] = 1
  # 发送一次notify大概在1个小时左右，设置2小时后删除当前文章状态
  setTimeout (-> delete gForumNotifyObj[postid]) , 2 * 60 * 1000

pushNotifyUsers = ({req, resp, tl, postid, gid, uids, groupUnread, realtorOnly})->
  msg = "Push Forum #{tl}; gid=#{gid}; realtorOnly=#{realtorOnly}"
  debug.info "Push Forum #{tl}; gid=#{gid}; realtorOnly=#{realtorOnly}"
  # in appweb.log
  console.log msg
  done = (msg)->
    resp.send {ok:1,msg}
    done = (msg)->
      debug.info msg
  
  #only send to users who can read chinese now. TODO:change to user post.lang
  l10n = i18n.getFun 'zh-cn'
  
  # 构建任务数据
  taskData = 
    msg: l10n('Forum') + ':' + tl
    from: "#{l10n('RealMaster')}(#{l10n('Forum')})"
    url: '/1.5/forum?postid='+postid
    priority: 100 # 论坛推送优先级
  targetUser = {}
  targetUser.gid = gid if gid
  targetUser.uids = uids if uids
  targetUser.realtorOnly = realtorOnly if realtorOnly
  targetUser.groupUnread = groupUnread if groupUnread
  targetUser.isDev = req.isDeveloperMode() or (/\d\.realmaster\./i.test req.host) or (req.param 'dev')
  taskData.targetUser = targetUser
  
  if gid
    taskData.url = taskData.url + '&gid='+gid
    return resp.send {ok:0, e:'No memebers to notify'} unless uids?.length

  Cache.destroy 'undefined.headline'
  
  try
    # 保留对其他内容的处理
    await ForumModel.updatePNAsync {postid,groupUnread,gid}
  catch err
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
  
  setTimeout (()-> done('Push Forum Working In background')
  ), 5000
  
  try
    # 创建推送任务而不是直接推送
    await TaskModel.createTask taskData
    debug.info "Push Forum Task Created for postid: #{postid}"
    setNotifyObj {status:'created',postid}
    return done('Push Forum Task Created Successfully')
  catch err
    debug.error "Push Forum Task Creation Err:#{err}"
    setNotifyObj {status:'err',postid}
    return resp.send {ok:0, e: err}

#1.5/forum/pushNotify
POST 'pushNotify', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless UserModel.accessAllowed 'forumAdmin', user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}

    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
    tl = params.tl
    id = params.id
    gid = params.gid
    realtorOnly = params.realtorOnly
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless (tl and id)
    # 检查文章2个小时之内是否推送过
    if gForumNotifyObj[id]?
      if gForumNotifyObj[id] is 'publishing'
        return respError {clientMsg:req.l10n('Publishing'), resp}
      else
        return respError {clientMsg:req.l10n('Already published'), resp}
    else
      setNotifyObj {status:'publishing',postid:id}
    pushParams=
      req:req
      resp:resp
      tl:tl
      postid:id
      realtorOnly:realtorOnly
    if gid
      GroupModel.getMembersuidById gid,(err, uids)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        pushParams.gid = gid
        pushParams.uids = uids
        pushParams.groupUnread = false
        return pushNotifyUsers pushParams unless params.groupUnread
        ForumModel.findGroupForumReaders id,(err,result)->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          readUsers = result?.readers?.filter((reader)->
            return reader.status is 'read'
            )
          if readUsers
            readuids = readUsers.map((user)->
              return user.uid.toString()
              )
            uids = uids.filter((id)->
              return readuids.indexOf(id.toString()) < 0
            )
          pushParams.uids = uids
          pushParams.groupUnread = true
          return pushNotifyUsers pushParams
    else
      pushParams.groupUnread = false
      pushNotifyUsers pushParams

GET (req,resp)->
    # if (req.getDevType() is 'app') or (UserModel.accessAllowed 'forumAdmin',user)
  UserModel.appAuth {req,resp},(user)->
    js = ['/js/vue3.min.js','/libs/jquery-1.9.1.min.js','/js/xpull.js',\
    '/js/overthrow/overthrow.min.js ','/js/entry/commons.js ',\
    '/js/axios.min.js', '/js/forum/indexPage.js']
    ctx =
      d: req.param('d') or '/1.5/index'
      js: js
      css: ['/css/apps/forum.css','/css/xpull.css','/css/apps/drawers.css']
      page:'news',
      appmode:req.cookies[libUser.APPMODE]
      lang:req.locale()
      isChinaIP:req.isChinaIP()
      os: if req.isIOSDevice() then 'ios' else 'android'
      isApp: req.getDevType() is 'app'
    libAppVer.getAppUpgradeSetting req, {os:ctx.os}, (setting) ->
      ctx.appUpgrade = setting
      return resp.ckup 'forum/home',ctx,'_',{noAngular:true, noref: true}
    # else
    #   return resp.send 'No auth'

# /1.5/forum/query
POST 'query', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp},(user)->
    if ['my_post', 'my_reply', 'my_favourite','my_group','my_draft'].indexOf(params?.category) > -1
      return resp.send {ok:0, url:'/1.5/user/login'} unless user
    getForumList req, params, user, (err, forums) ->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      resp.send {ok:1, forums:forums}


GET 'cmntnum/:id', (req,resp) ->
  id = req.param('id')
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
  gid = req.param('gid')
  ForumModel.findDetail {id,gid,isProp:true},(err,post)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    if not post
      cmntnum = 0
    else if post.del or post.discmnt
      cmntnum = -1
    else
      cmntnum = post.cc
    return resp.send {ok:1, cmntnum: cmntnum}

findRelatedPosts =(post, req, resp, cb) ->
  return cb(null) unless post?.tpbl?.length or post?.tp
  return cb(null) unless (post?.tpbl?.length or post?.tp)
  post.gid = req.param 'gid'
  ForumModel.findRelated post,(err,posts)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    relatedPosts = []
    if posts?.length
      for p in posts
        if p?._id?.toString() != post._id.toString()
          relatedpost = {tl: p.tl, _id:p._id, tp: p.tp, tpbl: p.tpbl, thumb: p.thumb}
          if p.tp
            relatedPosts.unshift(relatedpost)
          else
            relatedPosts.push(relatedpost)
    return cb(null, relatedPosts)

POST 'recommend', (req,resp) ->
  gid = req.param 'gid'
  similars = req.param 'similars'
  ForumModel.findRecommend {gid,similars}, (err,posts)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    uid = req.param 'aid'
    if uid and (helpers.isObjectIDString uid)
      #if uid is in bad format, reject.
      getRcommendCardList uid, (err, cards) ->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        return resp.send {ok: 1, posts:posts} unless cards?.length
        UserModel.findPublicInfo {lang:req.locale(),id:uid},(err, retu) ->
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
          resp.send {ok: 1, posts:posts, wecards:cards, userid:retu?.id}
    else
      return resp.send {ok: 1, posts:posts}

GET 'findPostByTp/:tp', (req,resp) ->
  tp = req.param('tp')
  gid = req.param('gid')
  ForumModel.findByTp {tp,gid},(err,_post)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n('Post not found'), resp} unless _post
    return resp.send {ok: 1, postid: _post._id}

POST 'detail/:id', (req,resp) ->
  id = req.param('id')
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
  getForumDetail req, resp, id, (err, ret) ->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send ret

GET 'edit', (req,resp)->
  if req.redirectHTTP()
    return
  editAuthCheck req,resp,'forum-edit'

GET 'webedit', (req,resp)->
  editAuthCheck req,resp,'web-forum-edit'

delPost = (wpHosts, id, del, req, resp) ->
  gid = req.param 'gid'
  ForumModel.delete {id,del,gid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    deleteFromWp id, wpHosts if del
    return resp.send {ok:1}


delPostFromDb = (wpHosts, id, req, resp) ->
  gid = req.param 'gid'
  ForumModel.deleteFromDb {id,gid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.l10n('Delete post failed'), resp} unless ret.result.n
    deleteFromWp id, wpHosts
    return resp.send {ok:1}
# setReadersStaus = DEF 'setReadersStaus'
updatePost = (post, history, req, resp, promptTemplates, cb)->
  # 如果promptTemplates是函数（向后兼容），则调整参数
  if typeof promptTemplates is 'function'
    cb = promptTemplates
    promptTemplates = []

  #todo add history change later.
  gid = req.param 'gid'
  ForumModel.updateOne {post, history, gid, promptTemplates}, (err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return cb() unless gid
    ForumModel.setReadersStaus {gid,id:post._id},(err, result)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return cb()

updateTopic = (oldtp, post, req, resp) ->
  gid = req.param 'gid'
  ForumModel.updateTopic {oldtp,post,gid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1}

delTopic = (oldtp, req, resp)->
  gid = req.param 'gid'
  ForumModel.deleteTopic {oldtp,gid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1}

POST 'updateMt', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = params.id
  gid = req.param 'gid'
  mt = new Date()
  ForumModel.updateMt {id,gid,mt},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1,mt:mt} unless gid = params.gid
    ForumModel.setReadersStaus {gid,id},(err, result)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return resp.send {ok:1,mt:mt}

#adInBtop used for  sort ad, put the ad in the bottom of top list,
getUpdatedPost = (set)->
  updateValsList  = [
    '_id',
    'tl',
    'm',
    'city',
    'prov',
    'cnty',
    'ncity',
    'tp',
    'tpbl',
    'src',
    'tags',
    'photos',
    'adInlist',
    # 'adIntop',
    # 'adInBtop',
    'rank',
    'adTop',
    'adTopPhoto',
    'adBottom',
    'adBottomPhoto'
    'showRequestInfo',
    'requestInfotl',
    'uid',
    'category',
    'subcate',
    'formid',
    'formName',
    'gid',
    'gnm'
    'reno',
    'meta_desc',
    'meta_keywords',
    'vidUrl',
    'vidRecord',
    'vidLive',
    'pid',
    'ohv',
    'ratio',
    'spuids',
    'spgids'
  ]
  post = {}
  for i in updateValsList
    if (v = set[i])?
      if i  is 'category'
        post[i] = v if v in allowed_category()
      else if i  is 'subcate'
        post[i] = []
        for d in v
          post[i].push d if d in allowed_sub()
      else
        post[i] = v
  post.thumb = if post.photos?.length then post.photos[0] else ''
  if helpers.hasChinese(post.m) or helpers.hasChinese(post.tl)
    post.lang = 'zh-cn'
  else
    post.lang = 'en'
  #TODO: ForumCol id 应该在model里生成。
  post.gid = new ForumCol.ObjectId(post.gid) if post.gid
  post.m = helpers.htmlEscape post.m
  post.tl = helpers.htmlEscape post.tl
  post.prov = ProvAndCity.getProvAbbrName post.prov if post.prov
  post.cnty = ProvAndCity.getCntyAbbrName post.cnty if post.cnty
  return post

setUserInfo = (post, user) ->
  if UserModel.accessAllowed('forumAdmin',user)
    return post
  post.fornm = user.fornm
  post.vt = new Date()
  post.avt = user.avt if user.avt
  return post

###*
 * 获取forum翻译模板
 * @returns {Promise<Array>} 翻译模板列表
###
getForumTranslationTemplates = () ->
  try
    templates = await PromptsModel.getCachedPromptTemplates('forum_translation', 'active')
  catch error
    debug.error 'Failed to get forum translation templates:', error
    return []
  return templates or []

POST 'edit', (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  del = params.del
  id = params.id
  wpid = params.wpid
  oldtp = params.oldtp
  save = params.save # 新增save参数，用于区分保存和发布
  web = req.param 'web'
  history = {}
  UserModel.appAuth {req,resp,userfields:['fornm','avt','mbl','noFltr','forBlk', 'pn', 'devId']},(user)->
    # 允许评房post
    # if not (UserModel.accessAllowed 'forumAdmin', user)
    #   return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    isGroupAdmin req, user, params.gid, (groupAdmin)->
      return resp.redirect 'http://'+req.host+'/www/login' if req.param 'web' and !user
      return resp.redirect '/1.5/user/login' unless user
      # resp.noCache()
      if del
        return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} if del not in ['recover','delete','db_delete']
        return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} if not id
        return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless groupAdmin or UserModel.accessAllowed 'forumAdmin', user
        return delPostFromDb  params.wpHosts, id, req, resp if del is 'db_delete'
        del = true if del is 'delete'
        del = false if del is 'recover'
        return delPost params.wpHosts, id, del, req, resp
      else
        return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} if not params.post
        # TODO:
        UserModel.isForumCommentBlocked {user}, (ret) ->
          if ret?[0]
            ret2 = {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),category:MSG_STRINGS.DEV_FORUM_BLOCKED,resp}
            # console.log '++++++',ret
            if ret?[1] is MSG_STRINGS.EMAIL_NOT_VERIFIED
              ret2.clientMsg = req.l10n 'Please verify your email address in order to publish posts.'
              ret2.category = MSG_STRINGS.EMAIL_NOT_VERIFIED
            return respError ret2
          if user.mbl
            try
              isPhoneNumberBlocked = await BlockPhoneModel.isPhoneNumberBlocked user.mbl
            catch err
              debug.error err
              return findErr(MSG_STRINGS.DB_ERROR,1)
            if isPhoneNumberBlocked
              return findErr(MSG_STRINGS.PHONE_BLOCKED,1)
          post = getUpdatedPost params.post
          checkContentObj =
            l10n:(a,b)->req.l10n a,b
            user:user
            collection:'forum'
            id: post._id
            content: [post.tl, post.m.replace(/<[^>]*>?/gm, '')]
          # checkContentObj.bypass = ['ad']
          checkContentObj.bypassAll = UserModel.accessAllowed 'forumAdmin',user

          # 统一的审核结果处理函数
          handleModerationResult = (err, ret) ->
            if err
              debug.error err
              return resp.send {ok:0, e:err.toString()}
            return resp.send {ok:0, e:ret.msg} if ret?.block
            # 继续原有的处理逻辑
            if post._id #do update,owner or admin can edit
              isOwner = user._id?.toString()  is  post.uid?.toString()
              unless isOwner or groupAdmin or UserModel.accessAllowed 'forumAdmin',user
                return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
              if isOwner
                post = setUserInfo post, user
              history.diff_m = params.diff_m if params.diff_m
              history.diff_tl = params.diff_tl if params.diff_tl
              history.diff_tp = params.diff_tp if params.diff_tp
              history.uid = user.uid if post.uid is not user.uid
              history.diff_tag = params.diff_tag if params.diff_tag
              history.diff_photo = params.diff_photo if params.diff_photo
              history.city = params.diff_city if params.diff_city
              history.prov = params.diff_prov if params.diff_prov
              history.ts = new Date()
              post.uid = if post.uid then new User.ObjectId(post.uid) else user._id
              post.mt = new Date() if params.update_mt
              # 如果是保存操作，设置状态为草稿
              if save
                post.draft = true
              # 获取翻译模板
              try
                forumPromptTemplates = await getForumTranslationTemplates()
              catch error
                debug.error 'Failed to get translation templates:', error
                forumPromptTemplates = []
              updatePost post, history, req, resp, forumPromptTemplates, ->
                #update all the topics from tpbl related posts.
                return updateTopic oldtp, post, req, resp if params.tp_action  is  'edit'
                # delete all the topic from tpbl in relate posts
                return delTopic oldtp, req, resp if params.tp_action  is  'del'
                return resp.send {ok:1}
            else # new post.
              post.uid = user._id
              post = setUserInfo post, user
              post.ts = new Date()
              post.mt = new Date()
              # 如果是保存操作，设置状态为草稿
              if save
                post.draft = true
              post.history = []
              history.diff_m = post.m
              history.diff_tl = post.tl
              history.uid = post.uid
              history.diff_tag = post.tags
              history.diff_photo = post.photos
              # history.lang = post.lang
              history.city = post.city
              history.prov = post.prov
              history.ts = new Date()
              for element in ['rank','adIntop','adInBtop','adInlist','showRequestInfo','formid','formName'] # fix: when mt changed to string, it won't show up on list any more.
                delete post[element] unless post[element]
              post.history.push(history)

              # 获取翻译模板
              try
                forumPromptTemplates = await getForumTranslationTemplates()
              catch error
                debug.error 'Failed to get translation templates:', error
                forumPromptTemplates = []

              ForumModel.saveOne {post, gid:req.param('gid'), promptTemplates:forumPromptTemplates}, (err,ret)->
                return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
                _id = ret?.ops?[0]?._id or ret.insertedId
                return resp.send {ok:1, _id: _id}

          # 尝试使用LLM审核，失败时回退到原有审核方式
          try
            # 获取内容过滤提示词模版
            promptTemplates = await PromptsModel.getCachedPromptTemplates('comment_filter')
          catch error
            debug.error "获取提示词模版异常，回退到原有审核:", error
            # 异常情况下回退到原有审核方式
            checkTextContent checkContentObj, (err, ret) ->
              handleModerationResult(err, ret)
          # 使用LLM审核（如果有模版）
          if promptTemplates.length > 0
            checkTextContent checkContentObj, promptTemplates, (err, ret) ->
              if err
                debug.error "LLM论坛帖子审核失败，回退到原有审核:", err
                # 回退到原有审核方式
                checkTextContent checkContentObj, (err, ret) ->
                  handleModerationResult(err, ret)
              else
                handleModerationResult(err, ret)
          else
            # 没有模版，使用原有审核方式
            checkTextContent checkContentObj, (err, ret) ->
              handleModerationResult(err, ret)


POST 'delete',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id = req.body?.id
    ForumModel.findDetail {id,uid:user._id},(err,forum)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless forum
      ForumModel.deleteFromDb {id},(err,deleteSuccess)->
        resp.send {ok:1}

POST 'sticky/:id', (req, resp)->
  params = req.body
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params and params.type
  UserModel.appAuth {req,resp},(user)->
    {type,gid,sticky,realtorOnly} = params
    isGroupAdmin req, user, gid,(groupAdmin)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless groupAdmin or UserModel.accessAllowed 'forumAdmin',user
      id = req.param('id')
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
      gid = req.param 'gid'
      ForumModel.sticky {id,gid,type,sticky,realtorOnly},(err,ret)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        #return respError req.l10n('Sticky post failed'), resp unless ret.lastErrorObject.n
        return resp.send {ok:1}


POST 'noCpm/:id', (req, resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
    id = req.param('id')
    noCpm = params.noCpm
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id and typeof noCpm is 'boolean'
    gid = req.param 'gid'
    ForumModel.setNoCPM {gid,id,noCpm},(err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return resp.send {ok:1}

# dataMethods = DEF 'dataMethods'
dataMethods.globalTags = (req, user, cb)->
  SysData.findOne {_id: 'forum_tags'}, (err, data)->
    if err
      debug.error err
      return cb req.l10n(MSG_STRINGS.DB_ERROR) # function expect callback, always callback
    tags = []
    for tag in (data?.tags or [])
      if tag.global
        tags.push tag
    return cb(null, tags)

GET 'tags',(req,resp) ->
  # lang = req.param 'lang'
  # fileds = {_id:0, tags:{$elemMatch:{lang:lang}}} if lang elemMatch only return first record.
  UserModel.appAuth {req,resp},(user)->
    SysData.findOne {_id: 'forum_tags'}, (err, data)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      tags = []
      isAdmin = UserModel.accessAllowed 'forumAdmin', user
      for tag in (data?.tags or [])
        if tag.adminOnly
          tags.push tag if isAdmin or req.param('type') is 'all'
        else
          tags.push tag
      return resp.send {ok:1, tags: tags}

addTag = (tag, user, adminOnly, tagSortKey, req, resp)->
  newTag = {key:tag, uid: user._id, ts: new Date(), adminOnly: adminOnly}
  newTag.sort = tagSortKey if tagSortKey
  # duplicate check
  SysData.findOne {_id: 'forum_tags','tags.key': tag},(err,data)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return respError {clientMsg:req.__('tag %s already exists.',tag), resp} if data
    SysData.findOneAndUpdate {_id: 'forum_tags'},{$push: {tags:newTag}},{upsert:true}, (err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return respError {clientMsg:req.l10n('Update tag failed'), resp} unless ret.lastErrorObject.n
      return resp.send {ok:1, newTag: newTag}

delTag = (tag, req, resp)->
  SysData.updateOne {_id: 'forum_tags'},{$pull:{tags:{ key:tag}}},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    # return respError req.l10n('Delete tag failed'), resp unless ret.lastErrorObject.n
    gid = req.param 'gid'
    ForumModel.deleteTag {tag,gid},(err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return resp.send {ok:1}

editTag = (newTag, tag, req, resp)->
  SysData.updateOne {_id: 'forum_tags', 'tags.key':tag},{$set:{'tags.$':newTag}},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    # return respError req.l10n('Edit tag failed'), resp unless ret.lastErrorObject.n
    #edit tags in all the post.
    gid = req.param 'gid'
    ForumModel.editTag {gid,newTag,tag},(err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      return resp.send {ok:1, tag: newTag}

POST 'tag',(req,resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user and UserModel.accessAllowed 'forumAdmin', user
    tag = params.tag
    adminOnly = params.adminOnly
    tagSortKey = params.tagSortKey
    action = params.action
    newTag = params.newtag
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless tag
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless ['add','del','edit'].indexOf(action) > -1

    tag = tag.trim()

    req.setupL10n()
    if action is 'add'
      addTag tag, user, adminOnly, tagSortKey, req, resp
    else if action is 'del'
      delTag tag, req, resp
    else if action is 'edit'
      return respError {clientMsg:req.l10n('No new tag value'), resp} unless newTag
      newTag = {key:newTag, uid: user._id, ts: new Date(), adminOnly: adminOnly}
      newTag.sort = tagSortKey if tagSortKey
      editTag newTag, tag, req, resp

GET 'topics',(req,resp) ->
  gid = req.param 'gid'
  ForumModel.findAllTopics {gid}, (err,data)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    return resp.send {ok:1, topics: data}

GET 'error', (req, resp)->
  errorPage(req, resp)

errorPage = (req, resp) ->
  cfg = {}
  cfg.hasU = 1
  cfg.avt  = '/img/icon_Alert.png'
  cfg.nm   = 'RealMaster'
  cfg.cardUrl  = req.fullUrl('/getapp')
  cfg.noWeCard = 1
  cfg.err = 1
  cfg.noLoading = 1
  if (req.getDevType() is 'app')
    cfg.noGetApp = 1
  cfg.shareInfoText = req.l10n "Can't find the Forum. May be deleted by the owner."
  return resp.ckup 'NewsMiddleAdPageEn', cfg, 'adPage', {jump:0}

getCpm = DEF 'getCpm'


#given http://youtube/watch?id=xxxx -> xxx
extractID = (url)->
  regExp = /.*(?:youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=)([^#\&\?]*).*/
  match = url.match(regExp)
  return if (match and match[1].length is 11) then match[1] else false

isErrorForumDetail = (err, ret, src)->
  if err
    return err
  # no content, generate new for prop/building only
  if not ret?.post
    if src not in ['property','building']
      return true
  false

getForumDetailPage2 = (req, resp) ->
  id = req.param('id')
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,userfields:'all'},(user)->
    src = req.param('src')
    inFrame = req.param('inFrame')
    fromUrl = req.param('from')
    iswebAdmin = req.param('iswebAdmin')
    isForumAdmin = UserModel.accessAllowed 'forumAdmin',user
    getForumDetail req, resp, id, (err, ret)->
      if isErrorForumDetail(err, ret, src)
        return errorPage(req, resp)
        
      # 使用通用方法判断是否满足 .cn 域名且是个人帖子的条件
      if await ForumModel.isPersonalPostInCnDomain(req.host, ret?.post)
        return resp.ckup 'generalError', {err_tran: req.l10n MSG_STRINGS.CONTACT_SERVICES}
        
      # property comments
      if (not ret) and (req.param('src') in ['property','video'])
        post = {}
        post.tl = req.param('tl') or ''
        post.m = ('ID: ' + req.param 'sid') if req.param 'sid'
        post.src = req.param('src')
        post.shareDesc = getShareDesc post.m
        post.shareImage =getShareImg req, post
        # 'http://www.realmaster.cn/img/no-pic.png'
        post.photos = if req.param 'img' then [req.param 'img'] else ["#{gShareHost}/img/no-pic.png"]
        post.city = req.param('city') or []
        post.prov = req.param('prov') or []
        post.status = 'new'
        post._id = req.param 'id'
      else if (not ret) and (req.param('src') in ['building'])
        editUrl = req.url.replace('details','edit')
        return resp.redirect editUrl
      else
        post = ret.post
        relatedPosts = ret.relatedPosts
        authorIsVip = ret.authorIsVip
        post.m = post.m?.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
        # NOTE: photos在前面已经处理过,这里再次添加会导致文章末尾再次出现图片
        # if post.thumb and not post.photos?.length
        #   post.photos = [post.thumb]
        if post.src is 'property'
          if req.param 'img'
            post.photos = [req.param 'img']
          else
            # 'http://www.realmaster.cn/img/no-pic.png'
            post.photos=["#{gShareHost}/img/no-pic.png"] unless post.photos?.length
        obj = helpers.pictureReplace(post.m, post.photos)
        post.m = obj.txtDone
        post.shareDesc = getShareDesc post.m
        post.shareImage =getShareImg req, post
      if post.ohv
        state = calcIsLiving(post.ohv)
        post.isLiving = state[0]
        post.passedLive = state[1]
        post.liveTime = formatLiveTime(post.ohv)
  
      getDispVar req, user,(err, ret)->
        dispVar = ret.datas  or  {}
        # title = (post?.tl  or  '') + ' ' + req.l10n('RealMaster') + '(www.realmaster.com)'
        title = "#{req.l10n('RealMaster')}: #{(post?.tl  or  '')}"
        cfg = {noAngular:1,noUserModal:1,noref:1,desc:post.shareDesc,shareImage:post.shareImage}
        opt ={dispVar:dispVar, from:req.param('from'),title:title, post:post,inFrame:inFrame, iswebAdmin: iswebAdmin, relatedPosts:relatedPosts,authorIsVip:authorIsVip, isForumAdmin: isForumAdmin}
        #get cpm for forum header
        opt.isApp = dispVar.isApp
        opt.share = req.param 'share'
        opt.city = dispVar.userCity?.o
        opt.prov = ProvAndCity.getProvAbbrName  dispVar.userCity?.p
        if (not post.adTopPhoto) and (not post.noCpm)
          cpm= getCpm req,'NB1',user
          if cpm.action is 'eml'
            cpm.computedHref =  'mailto:'+ cpm.ad_eml
          else if cpm.action is 'tel'
            cpm.computedHref = 'tel:'+ cpm.mbl
          else
            cpm.computedHref =  'javascript:void(0);'
          if not cpm.card
            cpm.moreDivClass = 'top'
          else
            cpm.moreDivClass =''
          opt.cpm = cpm
        if req.getDevType() is 'app'
          return resp.ckup 'forum-detail-new',opt, '_', cfg
        if fromUrl is 'forumList'
          opt.fromForumList = 1
          cfg.form = 'forum'
          return resp.ckup 'forum-detail-new',opt, 'layout', cfg
        getWXConfig req,req.fullUrl(),(err,wxcfg)->
          cfg.wxcfg = wxcfg
          resp.ckup 'forum-detail-new',opt, '_', cfg

GET 'details',(req,resp) ->
  getForumDetailPage2 req, resp

POST 'shared',(req,resp)->
  error = (err) ->
    return resp.send {ok:0, err:err}
  return error('No id') unless id = req.param('id')
  gid = req.param 'gid'
  ForumModel.incShared {id,gid}, (err)->
    return error('Not Updated') if err
    return resp.send {ok:1}

getPostFromParams = (id, req, cb) ->
  post = {}
  tl = req.param 'tl'
  img = req.param 'img'
  city = req.param 'city'
  prov = req.param 'prov'
  prov = ProvAndCity.getProvAbbrName prov
  src = req.param 'src'
  post =
    _id : id
    refid: id
    src : req.param 'src'
    ts : new Date()
    mt : new Date()

  if (src in ['property','video']) and (sid = req.param 'sid')
    post.m = "ID: #{sid}"
    post.sid = sid
    post.pid = req.param('pid')
    if src is 'video'
      post.ratio = '16:9'
      post.ohv = req.param('ohv')

  post.tl = tl if tl
  if img and (img not in ['undefined','null'])
    post.thumb = img
    post.photos=[]
    post.photos.push img
  post.city = city if city
  post.prov = prov if prov

  cb(null, post)


getPostFromNews = (id, req, resp, cb) ->
  post = {}
  News.findOneAndUpdate {_id:id}, {$set:{in_forum:true}}, {upsert:false,returnDocument:'before'}, (err,ret)->
    unless news = ret?.value
      debug.error err if err
      return resp.send {ok:0, e:req.l10n 'Can not find the News' }
    post =
      _id:  news._id
      src:  'news'
      tl:   news.tl
      m:    news.m  or  news.desc
      thumb: news.thumb
      ts:   news.dt or new Date()
      mt:   new Date()
      history: []
      lang: news.lang
      news_url: news.url
      news_chnl: news.chnl
      news_logo: news.logo
      news_src:  news.src

    if news.thumb  and  news.thumb.length < 1000
      post.photos = [news.thumb]
    post.m = post.m.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') if 'string' is typeof post.m
    cb(null, post) # err,post

getPostFromWecard = (id, req, resp, cb)->
  post = {}
  # publish forum from other source
  Wecard.findOneAndUpdate {_id:id}, {$set:{in_forum:true}}, {upsert:false,returnDocument:'before'}, (err,ret)->
    unless wecard = ret?.value
      debug.error err if err
      return resp.send {ok:0, e:req.l10n 'Can not find the News' }

    post =
      _id : wecard._id
      src :'news'
      tl : wecard.meta.title
      thumb: wecard.meta.img
      ts : wecard.meta.ts
      mt : new Date()
      history : []
      news_url : 'http://'+ req.host + '/1.5/wecard/prop/' + wecard.uid + '/' + wecard._id
      news_chnl : 'WePage'
      news_src : 'RealMaster'
      m : ''
    if wecard.meta.img  and  wecard.meta.img.length < 1000
      post.photos = [wecard.meta.img]
    headerStr = '<div style=\'padding-top:20px; padding-bottom:20px;\'>'
    headerStr += '<h2>' + wecard.meta.title + '</h2>'
    time = (if wecard.meta.ts then new Date(wecard.meta.ts).toDateString() else new Date().toDateString())
    headerStr += '<br>' + '<span style=\'margin-right:10px;\'>' + time + '</span>'
    headerStr += '<span style=\'margin-right:10px;\'><a>' + wecard.meta.editor + '</a></span>'
    if wecard.meta.addr
      url = req.exMapURL() + encodeURIComponent(wecard.meta.addr)
      headerStr += "<span style='color: #007aff;' class='fa fa-location-arrow' onclick='RMSrv.showInBrowser('#{url}')'></span>"
    headerStr += '</div>'
    post.m += headerStr

    i = 0
    if wecard?.seq?.length
      while i <= wecard.seq.length - 1
        post.m += wecard.seq[i].m
        i++
    cb(null, post)

updateForum = ({post,uid,gid}, req, resp)-> # err, post
  ForumModel.updateOne {post,uid,gid},(err,ret)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
    resp.send {ok:1, msg:'published!'}

GET 'publish/:id', (req,resp) ->
  id = req.param('id')
  src = req.param('src')
  gid = req.param('gid')
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
  unless src in ['news','wecard','property','psch','sch','video']
    sysErr = "src wrong when publish to forum: id #{id}, src: #{src}"
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp, sysErr}
  UserModel.appAuth {req,resp,userfields:['mbl','noFltr', 'forBlk', 'pn', 'devId']},(user)->
    return resp.redirect 'http://'+req.host+'/www/login' if req.param 'web' and !user
    return resp.redirect '/1.5/user/login' unless user
    # TODO:
    UserModel.isForumCommentBlocked {user}, (ret) ->
      if ret?[0]
        ret2 = {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),category:MSG_STRINGS.DEV_FORUM_BLOCKED,resp}
        # console.log '++++++',ret
        if ret?[1] is MSG_STRINGS.EMAIL_NOT_VERIFIED
          ret2.clientMsg = req.l10n 'Please verify your email address in order to publish posts.'
          ret2.category = MSG_STRINGS.EMAIL_NOT_VERIFIED
        return respError ret2
      if user.mbl
        try
          isPhoneNumberBlocked = await BlockPhoneModel.isPhoneNumberBlocked user.mbl
        catch err
          debug.error err
          return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
        if isPhoneNumberBlocked
          return respError {clientMsg:req.l10n(MSG_STRINGS.PHONE_BLOCKED), resp}
      req.getProjShareUID user, (err,uid)->
        if src  is  'news'
          return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user and UserModel.accessAllowed 'newsAdmin',user
          getPostFromNews id, req, resp, (err, post)->
            return updateForum {post,uid,gid}, req, resp
        else if src  is  'wecard'
          hasAccess = user and (UserModel.accessAllowed('newsAdmin',user) or UserModel.accessAllowed('vipAlliance',user))
          return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless hasAccess
          getPostFromWecard id, req, resp, (err, post)->
            return updateForum {post,uid,gid}, req, resp
        else if src in ['property' ,'psch','sch','video']
          getPostFromParams id, req, (err, post)->
            return updateForum {post,uid,gid}, req, resp


testfornm = (fornm, user, cb) ->
  return cb 'Must not empty' unless fornm?.trim()
  return cb 'Nickname too long' if fornm.length> 20

  format_fornm = fornm.toLowerCase().replace(/\s/g, '')

  for word in ['realmaster', '房大师', 'wechat']
    if format_fornm.indexOf(word)>= 0
      return cb 'Reserved keyword not allowed' unless UserModel.accessAllowed 'forumAdmin',user

  specialstr = ['>','<','=']
  for str in specialstr
    return cb 'Illegal characters' if format_fornm.indexOf(str)>= 0

  format_fornm = format_fornm.replace(/[&\/\\#,+()$~%.'":*?<>{}!^_-]/g,'')
  return cb 'Too many numbers' if (/\d{8,}/.test(format_fornm))

  cb(null)

POST 'setFornm',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.redirect 'http://'+req.host+'/www/login' if req.param 'web' and !user
    return resp.redirect '/1.5/user/login' unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
    fornm = params.fornm
    testfornm fornm, user, (err)->
      return respError {clientMsg:req.l10n(err), resp} if err
      UserModel.findByForumName fornm,(err, user) ->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
        return respError {clientMsg:req.l10n('nickname is used, please choose another one'), resp} if user
        UserModel.updateForumName {req,fornm},(err)->
          if err then return respError {clientMsg:err, resp}
          resp.send {ok:1,message:req.l10n('Saved')}

POST 'block',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return resp.redirect '/1.5/user/login' unless user
    return resp.send {ok:0,e:req.l10n('Missing Forum Id')} unless id = req.body?.id
    gid = req.param 'gid'
    ForumModel.blockUser {id,user,gid},(err,ret)->
      if err then return respError {clientMsg:err,resp}
      resp.send {ok:1,message:req.l10n('Blocked!')}

POST 'updateSplang',(req,resp)->
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:0,e:req.l10n('Login required')} unless user
    splang = req.body.splang or []
    return resp.send {ok:0,e:req.l10n('Please select your languages')} if splang.length is 0
    #TODO: splang设置之后怎么使用？用来过滤列表吗？
    UserModel.updateSpeakLanguage {req,splang},(err,user)->
      if err then return respError {clientMsg:err, resp}
      resp.send {ok:1}

POST 'blockCmnt',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,e:req.l10n('Login required')} unless user
    type = req.body.type #cmnt,user,post
    return resp.send {ok:0,e:req.l10n('Missing block type')} unless type
    forumId = req.body.forumId
    cmntIdx = req.body.cmntIdx
    uid = req.body.uid
    UserModel.blockComment user._id,{type,forumId,cmntIdx,uid},(err,msg)->
      if err then return respError {clientMsg:err, resp}
      msg = req.l10n msg if msg
      resp.send {ok:1,msg}

# checkUserRoleBeforeBlock = ({type,uid},cb)->
#   return cb() if (type in ['cmnt','post'])
#   return cb('Missing uid') unless uid
#   User.findOne {_id:uid},{fields:{roles:1}},(err,blkUser)->
#     return cb(err) if err
#     return cb(MSG_STRINGS.NOT_FOUND) unless blkUser
#     if blkUser.roles and accessAllowed('forumAdmin',blkUser)
#       return cb('User is admin')
#     cb()

# VIEW 'forum-home',->
#   div id: 'forumHome',style:'height: 100%;overflow: hidden;',->
#     text '<forum-home></forum-home>'
#   css '/css/apps/forum.css'
#   js '/libs/jquery-1.9.1.min.js'
#   css '/css/xpull.css'
#   js '/js/xpull.js'
#   js '/js/overthrow/overthrow.min.js'
#   js '/js/entry/commons.js'
#   # js '/js/entry/forumHome.js'
#   text "<script src='/js/entry/forumHome.js' async defer></script>"


VIEW 'forum-edit',->
  div id: 'forumEdit', ->
    text '<forum-edit></forum-edit>'
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  if @req.isAllowed 'forumAdmin'
    js '/libs/jquery-2.2.3.min.js'
    js '/libs/bootstrap-3.3.6.min.js'
    js '/js/sn2.mi.js'
    css '/css/summernote-bs3.css'
    css '/css/summernote.css'
  js '/js/entry/commons.js'
  js '/js/diff.min.js'
  js '/js/entry/forumEdit.js'
  css '/css/apps/forum.css'
  js '/js/ratchet.mi.js'

VIEW 'web-forum-edit',->
  div id: 'webForumEdit', ->
    text '<web-forum-edit></web-forum-edit>'
  js '/web/packs/commons.js'
  js '/js/diff.min.js'
  js '/web/packs/webForumEdit.js'
  css '/web/css/bootstrap.min.css'
  css '/web/css/common.css'
  css '/css/apps/forum.css'
  css '/web/css/wecards.css'
  js '/js/jquery-2.1.1.min.js'
  js '/web/packs/bootstrap.min.js'
