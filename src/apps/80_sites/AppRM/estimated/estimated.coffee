EstimatedModel = MODEL 'Estimated'
UserModel = MODEL 'User'
GroupModel = MODEL 'Group'
ShortUrl = MODEL 'ShortUrl'
{respError} = INCLUDE 'libapp.responseHelper'
{formatMtBasedOnYear} = INCLUDE 'lib.helpers_date'
{fullNameOrNickname} = INCLUDE 'libapp.user'
debug = DEBUG()
rateLimiter = INCLUDE 'lib.rateLimiterV2'
estimatedLimiterInstance = rateLimiter.createLimiter {AccessPerSessionLimit:30,source:'estimated'}
estimatedLimiterFilter = rateLimiter.createFilter estimatedLimiterInstance
APP 'estimated'

ESTIMATE_HIDDEN_BY_TEXT = 'The estimate value has been hidden by '
ESTIMATE_MODIFIED_BY_TEXT = 'The estimate value has been modified by '

###
# @description 检查用户是否有权限获取估价数据
# @param {object} params - 参数对象
# @param {string} params.propStatus - 房产状态
# @param {number} params.soldPrice - 售价
# @param {object} params.req - 请求对象
# @param {object} params.user - 用户对象
# @param {string} params.shortUrlId - 短链接ID
# @param {string} params.saleType - 销售类型
# @return {boolean} 是否有权限获取估价数据
###
checkEstimatePermission = ({propStatus, soldPrice, req, user, shortUrlId, saleType})->
  # 如果状态是A，app内直接返回true，如果是分享的需要查一下shortUrl是否正确
  if propStatus in ['A','Active']
    # 如果是分享链接，需要验证shortUrl
    if shortUrlId and shortUrlId.length
      try
        doc = await ShortUrl.getById shortUrlId
      catch err
        debug.error err
        return false
      # 验证分享类型是否为shareProp
      if doc?.type isnt 'shareProp'
        return false
    return true
  # 如果不是退市房源，且没有售价，则不允许查看
  if (saleType isnt 'Delisted') and (not soldPrice)
    return false
  unless user
    return false
  # 检查用户是否有noteAdmin/devGroup权限
  isNoteAdmin = req.isAllowed 'noteAdmin'
  isDevGroup = req.isAllowed 'devGroup'
  if isNoteAdmin or isDevGroup
    return true
  # 检查用户是否是inReal组成员
  try
    isInRealGroup = await GroupModel.isInGroup {uid: user?._id, groupName: ':inReal'}
  catch err
    debug.error err
    return false
  if isInRealGroup
    return true
  return false

###
# @description 计算蓝色区域（售价到估价最小值）的宽度百分比
# @param {object} params - 参数对象
# @param {number} params.estMin - 估价最小值
# @param {number} params.estMax - 估价最大值
# @param {number} params.soldPrice - 售价
# @return {number} 蓝色区域宽度百分比
###
getBlueSectionWidth = ({estMin,estMax,soldPrice})->
  # 计算总区间（从售价到最大估价）
  totalRange = estMax - soldPrice
  # 计算蓝色区域范围（从售价到最小估价）
  blueRange = estMin - soldPrice
  # 计算百分比
  percentage = (blueRange / totalRange) * 100
  return percentage

###
# @description 计算红色区域（最大估价到售价）的宽度百分比
# @param {object} params - 参数对象
# @param {number} params.estMin - 估价最小值
# @param {number} params.estMax - 估价最大值
# @param {number} params.soldPrice - 售价
# @return {number} 红色区域宽度百分比
###
getRedSectionWidth = ({estMin,estMax,soldPrice})->
  # 计算总区间（从最小估价到售价）
  totalRange = soldPrice - estMin
  # 计算红色区域范围（从售价到最大估价）
  redRange = soldPrice - estMax
  # 计算百分比
  percentage = (redRange / totalRange) * 100
  return percentage

###
# @description 计算圆点位置，确保不会超出边界
# @param {number} positionForPx - 计算出的像素位置
# @param {number} pointHalfWidth - 圆点宽度的一半
# @param {number} screenWidth - 屏幕宽度
# @return {string} 计算后的位置样式字符串
###
compNearBoundary = (positionForPx, pointHalfWidth, screenWidth)->
  # 如果位置小于圆点宽度的一半，固定在左侧
  if positionForPx <= pointHalfWidth
    return 'left:' + pointHalfWidth + 'px'
  # 如果位置大于屏幕宽度减去圆点宽度的一半，固定在右侧
  if positionForPx >= (screenWidth - pointHalfWidth)
    return 'right:-' + pointHalfWidth + 'px'
  # 其他情况返回计算出的位置
  return 'left:' + positionForPx + 'px'
  
###
# @description 计算售价黑色圆点在估价条的位置
# @param {object} params - 参数对象
# @param {number} params.estMin - 估价最小值
# @param {number} params.estMax - 估价最大值
# @param {number} params.soldPrice - 售价
# @param {number} params.range - 估价范围
# @param {number} params.pointHalfWidth - 圆点宽度的一半
# @param {number} params.percentForPx - 百分比对应的像素值
# @param {number} params.screenWidth - 屏幕宽度
# @return {object} 包含位置信息的对象
###
getSoldPointPost = ({estMin,estMax,soldPrice,range,pointHalfWidth, percentForPx, screenWidth})->
  soldPrice = Number(soldPrice)
  # 如果售价低于最小估价，固定在左侧
  if soldPrice < estMin
    blueWidth = getBlueSectionWidth({estMin,estMax,soldPrice})
    return {blueWidth: blueWidth + '%',greenWidth: (100 - blueWidth) + '%', soldPost: 'left:' + pointHalfWidth + 'px',direction: 'left'}
  # 如果售价高于最大估价，固定在右侧
  if soldPrice > estMax
    redWidth = getRedSectionWidth({estMin,estMax,soldPrice})
    return {redWidth: redWidth + '%',greenWidth: (100 - redWidth) + '%', soldPost: 'right:-' + pointHalfWidth + 'px',direction: 'right'}
  # 如果售价在估价范围内
  if (soldPrice >= estMin) and (soldPrice <= estMax)
    # 计算位置百分比
    position = 0
    if range
      position = ((soldPrice - estMin) / range) * 100
    position = Math.max(0, Math.min(100, position))
    # 转换为像素位置
    positionForPx = position * percentForPx
    soldPost = compNearBoundary(positionForPx, pointHalfWidth, screenWidth)
    # 根据位置确定对齐方式, 其中的数字14和84是在前端测量的位置，当游标在这些范围内时，游标上方的标签朝向位置，防止超出屏幕
    if position < 14
      return {soldPost: soldPost,greenWidth: '100%',direction: 'left'}
    else if position > 84
      return {soldPost: soldPost,greenWidth: '100%',direction: 'right'}
    else
      return {soldPost: soldPost,greenWidth: '100%',direction: 'center'}

###
# @description 计算估价圆点在估价条中的位置
# @param {object} params - 参数对象
# @param {number} params.estMin - 估价最小值
# @param {number} params.estMax - 估价最大值
# @param {number} params.estVal - 估价值
# @param {number} params.range - 估价范围
# @param {number} params.pointHalfWidth - 圆点宽度的一半
# @param {string} params.blueWidth - 蓝色区域宽度
# @param {string} params.greenWidth - 绿色区域宽度
# @param {number} params.soldPrice - 售价
# @param {string} params.redWidth - 红色区域宽度
# @param {number} params.percentForPx - 百分比对应的像素值
# @param {number} params.screenWidth - 屏幕宽度
# @return {object} 包含位置信息的对象
###
getEstimatePointPost = ({estMin,estMax,estVal,range,pointHalfWidth,blueWidth,greenWidth,soldPrice,redWidth,percentForPx,screenWidth})->
  value = Number(estVal)
  # 根据不同情况计算位置百分比
  if blueWidth
    range = estMax - soldPrice
    position = ((value - soldPrice) / range) * 100
  else if redWidth
    range = soldPrice - estMin
    position = ((value - estMin) / range) * 100
  else
    position = 0 # 防止最大值和最小值相等则除数变成0
    if range
      position = ((value - estMin) / range) * 100
  # 确保位置在0-100之间
  position = Math.max(0, Math.min(100, position))
  # 转换为像素位置
  positionForPx = position * percentForPx
  # 计算标签位置（向左偏移20px）
  posLabel = positionForPx - 20
  # 计算圆点位置
  estPost = compNearBoundary(positionForPx, pointHalfWidth, screenWidth)
  # 计算在sold情况下est. val标签的位置
  if blueWidth
    minValue = parseFloat(blueWidth) * percentForPx + 35
    if posLabel <= minValue
      posLabel = minValue
    else if posLabel >= (screenWidth - 70)
      posLabel = (screenWidth - 70)
  else if redWidth
    maxValue = parseFloat(greenWidth) * percentForPx - 70
    if posLabel <= 35
      posLabel = 35
    else if posLabel >= maxValue
      posLabel = maxValue
  else
    if posLabel <= 35
      posLabel = 35
    else if posLabel >= (screenWidth - 70)
      posLabel = (screenWidth - 70)
  # 其中的数字10和88是在前端测量的位置，当游标在这些范围内时，游标上方的标签朝向位置，防止超出屏幕
  if position < 10
    return {estPost: estPost,direction: 'left',posLabel:'left:'+posLabel+'px'}
  else if position > 88
    return {estPost: estPost,direction: 'right',posLabel:'left:'+posLabel+'px'}
  else
    return {estPost: estPost,direction: 'center',posLabel:'left:'+posLabel+'px'}

###
# @description 根据用户ID获取用户显示名称
# @param {string} uid - 用户ID
# @param {string} lang - 语言偏好
# @return {string} 用户显示名称或空字符串
###
getUserInfo = (uid, lang) ->
  unless uid
    return ''
  try
    userEdit = await UserModel.findByIdAsync uid, {projection: {nm: 1, nm_zh: 1,nm_en:1}}
  catch err
    debug.error "Failed to get user info for estimated:", err
    return ''
  if userEdit
    return fullNameOrNickname(lang, userEdit)
  return ''

###
# @description 获取估价修改的额外文本信息，包含修改类型和修改人
# @param {object} estimateData - 估价数据对象
# @param {string} lang - 语言偏好
# @return {string} 包含修改类型和修改人的文本
###
getExtraTxt = (estimateData, lang) ->
  if estimateData?.hideEstVal
    extraTxt = ESTIMATE_HIDDEN_BY_TEXT
  else
    extraTxt = ESTIMATE_MODIFIED_BY_TEXT
  unless estimateData?.rmlog?.length
    return extraTxt
  modifiedByUid = null
  # 从最新的rmlog条目开始查找（reverse遍历）
  for entry in estimateData.rmlog.slice().reverse()
    if estimateData?.hideEstVal
      # 如果当前是隐藏状态，查找最后一次隐藏操作的人员
      if entry.hideEstVal is true
        modifiedByUid = entry.uid
        break
    else if estimateData?.manResult
      # 如果当前不是隐藏状态且有手动修改值，查找最后一次修改值的人员
      if entry.sp_man_result?
        modifiedByUid = entry.uid
        break
  modifiedByNm = await getUserInfo(modifiedByUid, lang)
  return extraTxt + modifiedByNm
    
# /estimated/getEstimate 获取房产估价数据
POST 'getEstimate', (req, resp)->
  body = req.body
  propId = body?.propId
  propStatus = body?.propStatus
  soldPrice = body?.soldPrice
  pointHalfWidth = body?.pointHalfWidth
  shortUrlId = body?.shortUrlId
  saleType = body?.saleType
  screenWidth = body?.screenWidth
  returnResult = {ok: 1, result: null}
  # 验证必要参数
  unless propId and screenWidth and pointHalfWidth
    return respError {clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  # 使用限流器控制访问频率
  estimatedLimiterFilter req,resp,()->
    UserModel.appAuth {req, resp}, (user) ->
      if (not user) and (not shortUrlId)
        return respError {clientMsg: req.l10n(MSG_STRINGS.NEED_LOGIN), resp}
      # 检查权限, try在函数里已经检查
      hasPermission = await checkEstimatePermission({propStatus, soldPrice, req, user, shortUrlId, saleType})
      unless hasPermission
        return resp.send returnResult
      try
        # 有权限，获取估价数据
        estimateData = await EstimatedModel.getEstimateById(propId)
      catch err
        debug.error err, ' param:', req.body
        return respError {category: MSG_STRINGS.DB_ERROR, resp}
      unless estimateData?.estVal
        if propStatus in ['A','Active'] and (not shortUrlId)
          # 如果没有估价数据，检查是否可以估价
          try
            canEstimate = await EstimatedModel.canEstimate({
              prov: body.prov,
              ptype: body.ptype,
              ptype2: body.ptype2,
              onD: body.onD,
              merged: body.merged,
              saletp: body.saletp
            })
          catch err
            debug.error err, ' param:', req.body
            return resp.send returnResult
          if canEstimate
            return resp.send {ok: 1, result: {estVal: 'Waiting'}}
          return resp.send returnResult
        else
          return resp.send returnResult

      isInRealGroup = false
      if user and (estimateData?.hideEstVal or estimateData?.manResult?)
        try
          isInRealGroup = await GroupModel.isInGroup {uid: user._id, groupName: ':inReal'}
        catch err
          debug.error err
      isAdminOrInRealGroup = req.isAllowed('noteAdmin') or req.isAllowed('devGroup') or req.isAllowed('propAdmin') or isInRealGroup
      # 检查hideEstVal标记，如果为true且用户不是inReal组成员或admin，返回null
      if estimateData?.hideEstVal
        unless isAdminOrInRealGroup
          return resp.send returnResult
      l10n = (a,b)->req.l10n a,b
      # 如果有手动修改的值，优先显示手动修改的数据，否则显示估价数据
      estimateData.estMt = formatMtBasedOnYear(estimateData.estMt,l10n)
      estMin = estimateData.estMin = estimateData.manMin or Number(estimateData.estMin)
      estMax = estimateData.estMax = estimateData.manMax or Number(estimateData.estMax)
      estVal = estimateData.estVal = estimateData.manResult or Number(estimateData.estVal)

      range = estMax - estMin
      pointHalfWidth = Number(pointHalfWidth)
      screenWidth = Number(screenWidth)
      percentForPx = screenWidth / 100
      # 如果有售价，计算售价相关的位置信息
      if soldPrice
        estimateData.soldPrice = soldPrice
        {redWidth, soldPost, blueWidth, greenWidth, direction} = getSoldPointPost({estMin,estMax,soldPrice,range,pointHalfWidth, screenWidth,percentForPx})
        estimateData.soldPost = soldPost
        estimateData.direction = direction
        estimateData.redWidth = redWidth if redWidth
        estimateData.blueWidth = blueWidth if blueWidth
      # 计算估价点的位置信息
      {estPost, direction, posLabel} = getEstimatePointPost({estMin,estVal,range,pointHalfWidth,estMax,blueWidth,soldPrice,redWidth,greenWidth,percentForPx,screenWidth})
      estimateData.estPost = estPost
      estimateData.posLabel = posLabel
      unless soldPrice
        estimateData.direction = direction
      estimateData.greenWidth = greenWidth or '100%'
      # 如果是分享模式，模糊显示估价数据
      if shortUrlId
        estimateData.estMax = MSG_STRINGS.NEED_LOGIN
        estimateData.estMin = MSG_STRINGS.NEED_LOGIN
        estimateData.estVal = MSG_STRINGS.NEED_LOGIN
      if (not shortUrlId) and (estimateData?.hideEstVal or estimateData?.manResult) and isAdminOrInRealGroup
        estimateData.extraTxt = await getExtraTxt(estimateData, req.locale())
      deleteField = ['rmlog','manMin','manMax','manResult']
      for field in deleteField
        delete estimateData[field] if estimateData[field]
      return resp.send {ok: 1, result: estimateData}

# estimated/getAllEstimate 获取所有估价信息
POST 'getAllEstimate', (req, resp) ->
  unless propId = req.body?.propId
    return respError {category: MSG_STRINGS.BAD_PARAMETER, resp}
  UserModel.appAuth {req, resp}, (user) ->
    return respError {category: MSG_STRINGS.NEED_LOGIN, resp} unless user
    # 检查用户是否有propAdmin权限
    isPropAdmin = req.isAllowed 'propAdmin'
    try
      isInRealGroup = await GroupModel.isInGroup {uid: user._id, groupName: ':inReal'}
    catch err
      debug.error err
      return respError {category: MSG_STRINGS.DB_ERROR, resp}
    # 如果既不是propAdmin也不在inRealGroup中，则拒绝访问
    if not (isPropAdmin or isInRealGroup)
      return respError {category: MSG_STRINGS.NO_AUTHORIZED, resp}
    try
      # 获取所有估价信息
      estimateInfo = await EstimatedModel.getAllEstimateInfo(propId)
    catch err
      debug.error 'getAllEstimateInfo propId:', propId, err
      return respError {clientMsg:MSG_STRINGS.DB_ERROR, resp}
    unless estimateInfo
      return resp.send {ok: 1, result: null}
    return resp.send {ok: 1, result: estimateInfo}

# estimated/management - 获取估价数据用于管理页面
POST 'management', (req, resp) ->
  unless propId = req.body?.propId
    return respError {clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  UserModel.appAuth {req, resp}, (user) ->
    return respError {clientMsg: req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    # 检查用户是否有propAdmin权限
    isPropAdmin = req.isAllowed 'propAdmin'
    unless isPropAdmin
      return respError {clientMsg: req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      # 获取估价信息
      estimateData = await EstimatedModel.getEstimateById(propId)
    catch err
      debug.error 'estimates management propId:', propId, err
      return respError {clientMsg: MSG_STRINGS.DB_ERROR, resp}
    unless estimateData
      return resp.send {ok: 1, result: null}
    return resp.send {ok: 1, result: estimateData}

# estimated/manual - 更新手动估价数据
PUT 'manual', (req, resp) ->
  body = req.body
  unless body?.propId
    return respError {clientMsg: req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
  {propId, sp_man_result, sp_man_min, sp_man_max, hideEstVal} = body
  UserModel.appAuth {req, resp}, (user) ->
    return respError {clientMsg: req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
    # 检查用户是否有propAdmin权限
    isPropAdmin = req.isAllowed 'propAdmin'
    unless isPropAdmin
      return respError {clientMsg: req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      # 更新手动估价数据
      await EstimatedModel.saveManualEstimate({id: propId, sp_man_result, sp_man_min, sp_man_max, hideEstVal, uid: user._id})
    catch err
      debug.error 'estimates manual propId:', propId, err
      return respError {clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), resp}
    return resp.send {ok: 1}
