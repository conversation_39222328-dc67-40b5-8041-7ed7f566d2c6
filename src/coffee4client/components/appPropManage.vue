<template lang="pug">
div
  flash-message
  header#header-bar.bar.bar-nav(v-if="false")
    a.icon.fa.fa-back.pull-left(@click="closeEdit()")
    h1.title Edit Project
  //- div.bar.bar-standard.bar-footer
  //-   button.btn.btn-block.btn-long(@click="save()")
  div.backdrop(v-show="showBackdrop")
  div.modal.modal-55pc#ohInputModal
    div.bar.bar-standard.bar-footer
      a.btn.btn-sharp.btn-positive.btn-half.btn-fill(@click="addOhDate()") Ok
      a.btn.cancel.btn-sharp.btn-half.btn-fill(@click="toggleModal('ohInputModal'),showBackdrop=false") Cancel
    div.content
      div.date
        div.tl {{_('Date')}}
        input(type="date", v-model="newOhDate")
        div.day {{computedWeekDay}}
      div.hours
        div.from
          select(v-model="fromHour")
            option(v-for="h in hours", :value="h") {{h}}
          div.split
          select(v-model="fromMins")
            option(v-for="m in mins", :value="m") {{m}}
        div.text to
        div.to
          select(v-model="toHour")
            option(v-for="h in hours", :value="h") {{h}}
          div.split
          select(v-model="toMins")
            option(v-for="m in mins", :value="m") {{m}}
      div.row(v-if="ohMode=='ohs'")
        label type
        select(v-model="ohTp")
          option(value="P") {{_('Onsite','prop')}}
          option(value="L") {{_('Online','prop')}}
      div.row(v-if="ohMode=='ohs' && ohTp=='L'")
        label link
        input.input(v-model="ohLink",placeholder="virtual link")
  div.content
    div.oh-card(v-if='dispVar.isPropAdmin || dispVar.isRealGroup')
      div.tl {{_('Open House')}}
      div.ohs-wrapper
        div.oh(v-for="(oh,$index) in ohs")
          div
            span {{oh.f}}
            span.split -
            span {{oh.t.split(' ')[1]}}
            span(v-if="oh.tp") 
              span(v-if="oh.tp=='P'") ({{_('Onsite','prop')}})
              span(v-if="oh.tp=='L'") ({{_('Online','prop')}})
            span.fa.fa-rmclose(@click="removeOh($index,'ohs')")
        div.create-new(@click="toggleModal('ohInputModal'),showBackdrop=true,ohMode='ohs'")
          | {{_('Add Open House Time')}}

    div.manage-card.content-padded(v-if="dispVar.isPropAdmin || dispVar.isRealGroup")
      //- div.row.hide-opts
      //-   div
      //-     a.btn.btn-positive(href="javascript:;", @click="hideSqft(false)") show estimated sqft
      //-     a.btn.btn-negative(href="javascript:;", @click="hideSqft(true)") Hide estimated sqft
      //-   div
      //-     a.btn.btn-positive(href="javascript:;", @click="hideBuiltYr(false)") show estimated Built Year
      //-     a.btn.btn-negative(href="javascript:;", @click="hideBuiltYr(true)") Hide estimated Built Year
      div.row
        label rmSqft (Size):
        input.inputWidth(type="number", v-model="prop.rmSqft")
        div.btn-wrapper
          a.btn.btn-positive.verticalAlign(href="javascript:;", @click="updateProp({rmSqft:prop.rmSqft})") Save
          input.ceckboxml.verticalAlign(type="checkbox",name="hide",id="hideRmSqft",:checked="prop.hideSqft",@click="hideSqft(!prop.hideSqft)")
          label.checkboxLab(for="hideRmSqft") hide
      div.row
        label rmBltYr (Built Year):
        input.inputWidth(type="number", v-model="prop.rmBltYr")
        div.btn-wrapper
          a.btn.btn-positive.verticalAlign(href="javascript:;", @click="updateProp({rmBltYr:prop.rmBltYr})") Save
          input.ceckboxml.verticalAlign(type="checkbox",name="hide",id="hideBltYr",:checked="prop.hideBuiltYr",@click="hideBuiltYr(!prop.hideBuiltYr)")
          label.checkboxLab(for="hideBltYr") hide
    div.manage-card.content-padded(v-if="dispVar.isPropAdmin || dispVar.isRealGroup")
      div.row
        label youtube link:
        br
        input(type="text", v-model="prop.yturl")
        label youtube vid(auto-generate if has link):
        br
        input(type="text", v-model="prop.ytvid")
        //- div(style="margin:20px 0 20px 0;border-top:1px solid #f1f1f1;")
        label (cn) video link:
        br
        input(type="text", v-model="prop.vurlcn")
        label (cn) image link:
        br
        input(type="text", v-model="prop.vimgcn")
        div
          a.btn.btn-positive(href="javascript:;", @click="saveVid()") Save video
      div.row.hide-opts(v-if="dispVar.isPropAdmin && Object.keys(originalEstimate).length")
        label Manage Est. Value: &nbsp;
          span(style="font-weight: normal")
            span(v-if="hideEstVal") hide
            span(v-else) show
        a.btn.btn-negative(href="javascript:;", @click="hideEstValue(true)") hide est. value
        a.btn.btn-positive(href="javascript:;", @click="hideEstValue(false)") unhide est. value
        div.row(style="margin-top: 25px")
          label Manual Estimate Values (unit K): 
          div.estimate-inputs-row(style="margin-bottom:-10px")
            div.estimate-input-group
              label Min ({{displayOriginalValue(originalEstimate.estMin)}}):
              input(type="number", v-model="manualEstimate.min", placeholder="Min", step="1")
            div.estimate-input-group  
              label Result ({{displayOriginalValue(originalEstimate.estVal)}}):
              input(type="number", v-model="manualEstimate.result", placeholder="Result", step="1")
            div.estimate-input-group
              label Max ({{displayOriginalValue(originalEstimate.estMax)}}):
              input(type="number", v-model="manualEstimate.max", placeholder="Max", step="1")
        div
          a.btn.btn-positive.verticalAlign(href="javascript:;", @click="saveManualEstimate()") Save Manual Estimates
    div.oh-card(v-if="dispVar.isPropAdmin")
      div.tip (only propAdmin can edit live time)
      div.tl {{_('Open House')}} - {{_('Live Video(not used anymore)')}}
      //- div.desc {{_('Set Open House Time.')}}
      div.ohs-wrapper
        div.oh(v-for="(oh,$index) in ohvs")
          div
            span {{oh.f}}
            span.split -
            span {{oh.t.split(' ')[1]}}
            span.fa.fa-rmclose(@click="removeOh($index,'ohvs')")
        div.create-new(@click="toggleModal('ohInputModal'),showBackdrop=true,ohMode='ohvs'")
          | {{_('Add Video Open House Time')}}

    div.manage-card.content-padded(v-if="dispVar.isAdmin && isRMProp")
      a.btn.btn-positive(@click="manageEdm()") Edm
        span(v-if="prop_edm") （{{prop_edm}}）

    div.manage-card(v-if="dispVar.isPropAdmin")
      div.tip (only isPropAdmin can see below:)
      div.row
        button.btn.btn-negative(@click="removeTop()", v-show="isTopped") Remove topListing
      div.row
        label useMlatlng
          input(type="checkbox", v-model="prop.useMlatlng", @click="changeUseMlatlng()")
      //- div.row
      //-   label predFlw
      //-   input(type="checkbox", v-model="prop.predFlw", @click="changePredFlw()")
      div.row
        label priceChange
          input(type="checkbox", v-model="priceChange", @click="changePirceFlag()")
      div.row
        label tgr (num)
          input(type="number", v-model="prop.tgr")
        label park_spcs (num)
          input(type="number", v-model="prop.park_spcs")
        a.btn.btn-positive(href="javascript:;", @click="updateProp({gr:1})") Save gr
      //- div.row
      //-   label refreshPic
      //-   input(type="checkbox", v-model="prop.refreshPic", @click="changeRefreshPic()")
      div.row
        label Status:
        select(v-model="prop.status_en")
          option(value="U") U
          option(value="A") A
        div.btn-wrapper
          a.btn.btn-positive(href="javascript:;", @click="updateProp()") Save
      //- div.row
      //-   button.btn.btn-positive(@click="changePhodl()") change phodl and update pic num#
      div.row
        label daddr(display address):
        select(v-model="prop.daddr")
          option(value="Y") Y
          option(value="N") N
        div.btn-wrapper
          a.btn.btn-positive(href="javascript:;", @click="changeDaddr()") Save

      div.row
        label rmdpho(display photo):
        select(v-model="prop.rmdpho")
          option(value="Y") Y
          option(value="N") N
        div.btn-wrapper
          a.btn.btn-positive(href="javascript:;", @click="changeDpho()") Save
      div.row
        label dpred(display predict):
        select(v-model="prop.dpred")
          option(value="Y") Y
          option(value="N") N
        div.btn-wrapper
          a.btn.btn-positive(href="javascript:;", @click="changeDpred()") Save
        //- div.row
        //-   label Status:
        //-   select(v-model="prop.lst")
        //-     option(value="U") U
        //-     option(value="A") A
      div.row
        a.btn.btn-positive(href="javascript:;", @click="insertManualImport()") Insert to manual import
        div(v-show='importHist.ts')
          span Status: {{importHist.status}}
          br
          span Time: {{importHist.ts}}
      div.row.hide-opts
        | prop.del = {{prop.del}}
        a.btn.btn-negative(href="javascript:;", @click="deleteProp('hide')") hide this prop
        a.btn.btn-positive(href="javascript:;", @click="deleteProp('unhide')") unhide this prop
      div.row
        a.btn.btn-negative(href="javascript:;", @click="deleteProp('del')") Delete this prop forever
      hr
      div.row
        label Edit history
        div.row(v-for="log in prop.rmlog")
          span {{(log.uid||log._id||'').substr(0,9)}} --
          span {{log.k}}:
          span {{(log.v+''||'').substr(0,15)}}
      
</template>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
input[type=checkbox]{
  margin-left: 10px;
}
label{
  margin: 0;
  width: 100%;
}
.hide-opts a{
  margin-right: 10px;
}
.tip{
  font-size: 13px;
  color: #888;
  text-align: center;
  padding: 10px;
  margin-top: 20px;
  border-top: 1px solid #f1f1f1;
}
#ohInputModal .row {
  display: flex;
}
#ohInputModal .row label {
  margin-right: 10px;
}
#ohInputModal .row input {
  width: 80%;
}
#ohInputModal{
  z-index: 20;
  border-top: none;
}
#ohInputModal .date{
  padding: 5px 5px 5px 7px;
  font-size: 15px;
  /* text-align: center; */
  color: #666;
  position: relative;
}
#ohInputModal .date .tl{
  text-align: center;
}
#ohInputModal .date input{
  margin-top: 14px;
  /* width: 80%; */
  /* display: inline-block; */
  outline: none;
  border-radius: 0;
  -webkit-appearance: none;
  border: none;
  border-bottom: 1px solid #5cb85c;
}
#ohInputModal .date .day{
  /* width: 20%; */
  /* display: inline-block; */
  /* float: right; */
  /* margin-top: 10px; */
  /* z-index: 14; */
  position: absolute;
  top: 61px;
  right: 13px;
  font-size: 15px;
  font-weight: bold;
  color: black;
  /* display: inline-block; */
}
#ohInputModal .hours{
  padding: 0 7px 0 7px;
}
#ohInputModal .btn.cancel{
  color: black;
}
#ohInputModal .btn{
  padding-top: 13px;
  top: -1px;
  border-top: 1px solid #f1f1f1;
}
#ohInputModal .bar-footer{
  padding: 0;
}
#ohInputModal .hours .from,
#ohInputModal .hours .to{
  width: 46%;
  display: inline-block;
}
#ohInputModal .hours .text{
  width: 8%;
  display: inline-block;
  text-align: center;
}
#ohInputModal .hours .split{
  width: 8%;
  display: inline-block;
}
#ohInputModal select{
  width: 46%;
  margin-right: 3%;
  display: inline-block;
  margin: 0;
  height: 27px;
  line-height: 21px;
  background-color: #fff;
  border: none;
  border-radius: 1px;
  outline: none;
  -webkit-appearance: none;
  border-bottom: 1px solid #ddd;
}

.oh-card{
  padding: 0 10px;
  margin-bottom: 30px;
}
.oh-card .tl{
  /* border-bottom: 1px solid #f1f1f1; */
  padding: 18px 0;
  font-size: 17px;
  font-weight: bold;
}
.oh-card .desc{
  padding: 20px 0 20px 0;
  font-size: 13px;
  color: #666;
}
.oh-card .split{
  width: 37px;
  display: inline-block;
  text-align: center;
}
.oh-card .oh{
  margin: 20px 0;
  padding: 7px 0 2px 2px;
  border-bottom: 1px solid #5cb85c;
}
.oh-card .create-new{
  padding: 20px 0 0 2px;
  /* text-align: center; */
  color: #666;
  font-size: 14px;
  border-bottom: 1px solid #666;
}
.manage-card{
  padding: 0 10px;
}
.oh-card .oh .fa{
  color: #989898;
  font-size: 16px;
  float: right;
  padding: 5px;
  margin-top: -5px;
}
.manage-card .row{
  margin-bottom: 25px;
}
.manage-card .row div{
  margin-bottom: 10px;
}
.manage-card .row select{
  width: 50%;
  display: inline-block;
}
.row .btn-wrapper{
  display: inline-block;
  margin-left: 10px;
}
.btn-delete-prop {
  float: right;
  font-size: 10px
}

.verticalAlign{
  vertical-align:middle;
}
.manage-card .row .checkboxLab{
  width: auto;
  font-weight:normal;
  margin-left: 8px;
  vertical-align:middle;
}
.manage-card .row .inputWidth{
  width: 50%;
}
.manage-card .row .ceckboxml{
  margin-left: 15px;
}
.estimate-inputs-row{
  display: flex;
  gap: 15px;
  margin-top: 10px;
}
.estimate-input-group{
  flex: 1;
  display: flex;
  flex-direction: column;
}
.estimate-input-group label{
  font-size: 13px;
  margin-bottom: 5px;
  color: #666;
}
.estimate-input-group input{
  width: 100%;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 3px;
}
</style>

<script>
import FlashMessage from './frac/FlashMessage.vue'
import pagedata_mixins from './pagedata_mixins'

export default {
  mixins: [pagedata_mixins],
  components:{
    FlashMessage,
  },
  computed:{
    isRMProp:function () {
      return /^RM/.test(this.prop.id);
    },
    isTopped:function(){
      return new Date(this.prop.topTs) >= new Date()
    },
    computedWeekDay:function () {
      if (!this.newOhDate) {
        return null;
      }
      var day = new Date(this.newOhDate).getDay()
      if (day == 0) {
        return 'Mon'
      } else if (day == 1) {
        return 'Tue'
      } else if (day == 2) {
        return 'Wed'
      } else if (day == 3) {
        return 'Thr'
      } else if (day == 4) {
        return 'Fri'
      } else if (day == 5) {
        return 'Sat'
      } else {
        return 'Sun'
      }
    }
  },
  data () {
    return {
      showBackdrop:false,
      datas:[
        // 'jsGmapUrl',
        'isDevGroup',
        'isAdmin',
        'isPropAdmin',
        'isDevGroup',
        'isRealGroup'
      ],
      newOhDate:'',
      fromHour:'14',
      fromMins:'00',
      toHour:'16',
      toMins:'00',
      ohTp:"P",
      ohLink:"",
      priceChange:false,
      dispVar:{
        isAdmin:false,
        isPropAdmin:false,
        isDevGroup:false,
        isRealGroup:false
      },
      prop_edm: null,
      prop:{hideBuiltYr:false, hideSqft:false},
      ohs:[],
      ohvs:[],
      // ohvids:[],
      ohMode:'ohs',
      hours:[],
      mins:[],
      importHist:{},
      hideEstVal: false,
      originalEstimate: {},
      manualEstimate: {
        result: null,
        min: null,
        max: null
      }
      // checkRmSqft: false,
      // checkRmBltYr: false
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    window.gMapsCallback = this.initGmap;
    for (var i = 0; i < 24; i++) {
      let v = i+'';
      if (v.length < 2) {
        v='0'+v
      }
      self.hours.push(v);
    }
    for (var i = 0; i < 61; i+=5) {
      let v = i+'';
      if (v.length < 2) {
        v='0'+v
      }
      if (v == '60'){
        v='59'
      }
      self.mins.push(v);
    }
    if (!vars.id) {
      console.error('No Id specified');
      return RMSrv.dialogAlert('No Id specified!')
    } else {
      self.getPropDetail(vars.id);
      self.showVipInfo(vars.id);
    }
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.getEstimateData(vars.id);
    });
    self.newOhDate = self.parseDate(self.getNextSaturday())
    self.getPageData(self.datas, {}, true);
  },
  methods: {
    insertManualImport(){
      var self = this;
      if(self.prop.src == "RM"){
        return RMSrv.dialogAlert('Not support')
      }
      var params={id:self.prop.sid,src:self.prop.src};
      self.$http.post('/1.5/import/edit', params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            RMSrv.dialogAlert(ret.msg)
          } else {
            RMSrv.dialogAlert(ret.err);
            if(ret.ret){
              self.importHist = ret.ret
            }
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    saveVid(){
      this.updateProp({
        vurlcn:this.prop.vurlcn,
        ytvid:this.prop.ytvid,
        yturl:this.prop.yturl,
      })
    },
    removeOh(idx,mode){
      // console.log(idx);
      if (mode) {
        this.ohMode = mode;
      }
      this.addOhDate({mode:'remove',idx:idx});
    },
    getNextSaturday(){
      var t = new Date();
      t.setDate(t.getDate() - t.getDay()+6);
      return t;
    },
    parseDate(d){
      var month = (d.getMonth()+1);
      if (month<10) {
        month = '0'+month;
      }
      var day = d.getDate();
      if (day<10) {
        day = '0'+day;
      }
      return d.getFullYear()+'-'+month+'-'+day;
    },
    genIdFromOhv(ohv){
      return this.prop._id+'-'+ohv.f.replace(/\W/ig,'')+ohv.t.replace(/\W/ig,'');
    },
    genTlFromProp(ohv){
      return encodeURIComponent('Open House Live: '+this.prop.addr);
    },
    genOhvStrFromOhv(ohv){
      return encodeURIComponent(ohv.f+'-'+ohv.t.split(' ')[1]);
    },
    createForumContent(cb){
      var self = this;
      var len = self.ohvs.length;
      var ohv = self.ohvs[len-1];
      if (!ohv) {
        return;
      }
      var _id = self.genIdFromOhv(ohv);
      // create forum for this ohv
      var tl = self.genTlFromProp(ohv);
      let url = '/1.5/forum/publish/'+_id;
      let ohvStr = self.genOhvStrFromOhv(ohv);
      url = url +'?src=video&tl='+tl+'&city='+this.prop.city_en+'&prov='+this.prop.prov_en+'&img='+encodeURIComponent(vars.img)+'&sid='+this.prop.sid+'&ohv='+ohvStr+'&pid='+this.prop._id;
      self.$http.get(url).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            if(cb) {
              return cb();
            } else {
              RMSrv.dialogAlert(ret.msg||'Saved')
            }
          } else {
            RMSrv.dialogAlert(ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    removeForumContent(ohv){
      if (!ohv) {
        return;
      }
      var self = this;
      var _id = self.genIdFromOhv(ohv);
      // console.log('+++++remove',_id);
      var params={id:_id, city: self.prop.city_en, prov: self.prop.prov_en};
      params.del = 'db_delete';
      self.$http.post('/1.5/forum/edit', params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            RMSrv.dialogAlert('DB removed post: '+_id)
          } else {
            RMSrv.dialogAlert(ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
      // post to forum to delete this post; ohvids[0]
    },
    addOhDate(opt={}){
      var self = this;
      var ohMode = self.ohMode || 'ohs';
      var act = 'add'
      if (opt.mode == 'remove') {
        var idx = opt.idx;
        var tmp = this[ohMode].splice(idx,1);
        act = 'delete'
      } else {
        var ohl = this.genNewOhArray()
        this[ohMode].push(ohl);
      }
      self.$http.post('/1.5/prop/manage', {ohvs:this.ohvs,ohs:this.ohs,_id:vars.id,act}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.showBackdrop = false;
            toggleModal('ohInputModal','close');
            if (self.ohMode == 'ohvs') {
              if (opt.mode == 'remove') {
                self.removeForumContent(tmp[0])
              } else {
                self.createForumContent()
              }
            }
          } else {
            // console.error(ret.err);
            RMSrv.dialogAlert(ret.err||ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    genNewOhArray(){
      var ret = {};
      var oh0 = this.newOhDate+' '+this.fromHour+':'+this.fromMins;
      ret.f = oh0;
      var oh0t = this.newOhDate+' '+this.toHour+':'+this.toMins;
      ret.t = oh0t;
      if (this.ohMode=='ohvs') {
        ret.vid = this.genIdFromOhv(ret)
      } else {
        if (this.ohTp) {
          ret.tp = this.ohTp;
        }
        if (this.ohLink) {
          ret.l = this.ohLink;
        }
      }
      return ret;
    },
    toggleModal(a,b){
      toggleModal(a,b)
    },
    changeDpho(){
      var self = this;
      setTimeout(function () {
        self.updateProp({rmdpho:1})
      }, 400);
    },
    changeDpred(){
      var self = this;
      setTimeout(function () {
        self.updateProp({dpred:1})
      }, 400);
    },
    changeDaddr(){
      var self = this;
      setTimeout(function () {
        self.updateProp({daddr:1})
      }, 400);
    },
    changePirceFlag(){
      var self = this;
      setTimeout(function () {
        self.updateProp({priceChange:1})
      }, 400);
    },
    changePhodl(){
      this.updateProp({phodl:1})
    },
    manageEdm(){
      var self = this;
      var url = '/1.5/props/edm';
      var del = this.prop.edm ? 1 : 0
      self.$http.post(url, {_id: self.prop._id, del: del}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.prop.edm = ret.edm;
            if (ret.edm) {
              var edm = new Date(ret.edm)
              self.prop_edm =  edm.getFullYear() + '-' + (edm.getMonth() +1) + '-' +  edm.getDate() ;
            } else {
              self.prop_edm = null;
            }
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    changeUseMlatlng(){
      this.updateProp({useMlatlng:1})
    },
    changePredFlw(){
      this.updateProp({predFlw:1})
    },
    changeRefreshPic(){
      this.updateProp({refreshPic:1})
    },
    removeTop(){
      this.updateProp({removeTop:1})
    },
    initProp(){
      this.ohs = this.prop.ohz || [];
      // TODO: should use same naming convention as ohzv
      this.ohvs = this.prop.ohzv || [];
      if (this.prop.edm) {
        var emdts = new Date(this.prop.edm);
        this.prop_edm =  emdts.getFullYear() + '-' + (emdts.getMonth() +1) + '-' +  emdts.getDate() ;
      }
      if (this.prop.priceChange) {
        this.priceChange = true;
      }
      if (!/^TRB/.test(this.prop._id)) {
        return
      }
      var d = {_id:this.prop._id,sid:this.prop.sid};
      // var self = this;
      // self.$http.post('/1.5/props/trbdetail', d).then(
      //   function (ret) {
      //     ret = ret.data;
      //     if (!ret.e) {
      //       self.prop = Object.assign({
      //       vurlcn:null,
      //       vimgcn:null,
      //       yturl:null,
      //       ytvid:null},self.prop,ret.prop);
      //     } else {
      //       console.error(ret.e);
      //       // RMSrv.dialogAlert(ret.e);
      //     }
      //   },
      //   function (ret) {
      //     RMSrv.dialogAlert( "server-error" );
      //   }
      // );
    },
    getPropDetail(id){
      var d = {_id:id};
      //post to backend, let backend handle RM and case
      var self = this;
      self.$http.post('/1.5/props/detail', d).then(
        function (ret) {
          ret = ret.data;
          if (!ret.e) {
            self.prop =  Object.assign(self.prop,ret.detail||ret.prop) || {};
            self.initProp();
          } else {
            // console.error(ret.e);
            // RMSrv.dialogAlert(ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    extractVidFromUrl(url=""){
      var regExp = /.*(?:youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=)([^#\&\?]*).*/;
      var match = url.match(regExp);
      return (match&&match[1].length==11)? match[1] : false;
    },
    deleteProp(mode){
      var data = {
        _id:this.prop._id,
        delete:true
      };
      if (mode == 'hide') {
        data.hide = 1;
      }
      if (mode == 'unhide') {
        data.hide = 0;
      }
      var self = this;
      var confirmDelete = function(idx) {
        if (idx+'' != '2') {
          return;
        }
        _deleteProp();
      }
      var _deleteProp = function() {
        self.$http.post('/1.5/prop/manage', data).then(
          function (ret) {
            ret = ret.data;
            if (ret.ok) {
              // self.prop = Object.assign({},self.prop,ret);
              RMSrv.dialogAlert(JSON.stringify(ret));
              // window.bus.$emit('flash-message','Saved')
            } else {
              RMSrv.dialogAlert(ret.err);
            }
          },
          function (ret) {
            ajaxError(ret);
          }
        );
      }
      if (data.hasOwnProperty('hide')) {
        _deleteProp();
      } else {
        RMSrv.dialogConfirm('Are you sure to delete the property?',
          confirmDelete,
          "Message",
          ['Cancel', 'Confirm']
        );
      }

    },
    updatePicNum(){
      this.updateProp({rmPho:true})
    },
    useMaxPicNum() {
      this.updateProp({maxPhoDownload:true})
    },
    hideSqft(status){
      this.updateProp({hideSqft:status})
    },
    hideBuiltYr(status){
      this.updateProp({hideBuiltYr:status})
    },
    displayOriginalValue(value){
      // 显示原始值时除以1000，保留整数
      if (value == null || value === undefined) return 'N/A';
      return (Number(value)/1000).toFixed(2)+'K';
    },
    convertToDisplayValue(value){
      // 将数据库值转换为显示值（除以1000）
      if (value == null || value === undefined) return null;
      return Number((Number(value)/1000).toFixed(2));
    },
    convertToApiValue(value){
      // 将显示值转换为API值（乘以1000）
      if (value == null || value === undefined || value === '') return null;
      return Number(value) * 1000;
    },
    // 检查每个估值与原始值的差异是否超过50%
    checkValueDiff (newVal, origVal, type){
      if (newVal) {
        const originalResult = Number(origVal);
        const diffPercent = (Math.abs(newVal - originalResult) / originalResult) * 100;
        if (diffPercent > 50) {
          RMSrv.dialogAlert(`Manual ${type} value cannot differ more than 50% from original value`);
          return false;
        }
        return true;
      }
      return false;
    },
    hideEstValue(status){
      var self = this;
      if(!vars.id) return;
      
      let body = {
        propId: vars.id,
        hideEstVal: status
      };
      fetchData('/estimated/manual', {method:'PUT',body},(err,ret)=>{
        if (err || !ret.ok) {
          RMSrv.dialogAlert('Failed to update estimate visibility');
          return;
        }
        self.hideEstVal = status;
        RMSrv.dialogAlert('Estimate visibility updated successfully');
      });
    },
    getEstimateData(id){
      if(!this.dispVar.isPropAdmin){
        return;
      }
      var self = this;
      let body = {
        propId: id
      }
      fetchData('/estimated/management', {body},(err,ret)=>{
        if (err || !ret.ok) {
          RMSrv.dialogAlert('Failed to get estimate data');
          return;
        }
        self.originalEstimate = {
          estVal: ret.result.estVal,
          estMin: ret.result.estMin,
          estMax: ret.result.estMax
        };
        // 设置手动估价值（转换为K单位显示）
        self.manualEstimate = {
          result: self.convertToDisplayValue(ret.result.manResult),
          min: self.convertToDisplayValue(ret.result.manMin),
          max: self.convertToDisplayValue(ret.result.manMax)
        };
        // 设置hideEstVal状态
        self.hideEstVal = ret.result.hideEstVal ? true : false;
      });
    },
    saveManualEstimate(){
      var self = this;
      if(!vars.id) return;
      // 检查是否有任何手动输入的值
      if (!self.manualEstimate.result && !self.manualEstimate.min && !self.manualEstimate.max) {
        RMSrv.dialogAlert('Please ensure estimate values');
        return;
      }
      // 获取有效的值：如果用户没有输入，使用原始值（注意：输入值需要转换为实际值）
      var resultValue = self.manualEstimate.result? self.convertToApiValue(self.manualEstimate.result) : Number(self.originalEstimate.estVal);
      var minValue = self.manualEstimate.min? self.convertToApiValue(self.manualEstimate.min) : Number(self.originalEstimate.estMin);
      var maxValue = self.manualEstimate.max? self.convertToApiValue(self.manualEstimate.max) : Number(self.originalEstimate.estMax);
      // 验证数值的有效性
      if (isNaN(resultValue) || isNaN(minValue) || isNaN(maxValue)) {
        RMSrv.dialogAlert('Please ensure all estimate values are valid numbers');
        return;
      }
      // 校验与原始值的差异不超过50%
      if (!this.checkValueDiff(resultValue, self.originalEstimate.estVal, 'result') ||
          !this.checkValueDiff(minValue, self.originalEstimate.estMin, 'min') ||
          !this.checkValueDiff(maxValue, self.originalEstimate.estMax, 'max')) {
        return;
      }
      // 验证条件：min < max
      if (minValue >= maxValue) {
        RMSrv.dialogAlert('Minimum value must be less than maximum value');
        return;
      }
      
      // 验证条件：min <= result <= max
      if (resultValue < minValue || resultValue > maxValue) {
        RMSrv.dialogAlert('Result value must be between minimum and maximum values');
        return;
      }
      
      var body = {
        propId: vars.id,
        sp_man_result: resultValue,
        sp_man_min: minValue,
        sp_man_max: maxValue
      };
      fetchData('/estimated/manual', {method:'PUT',body},(err,ret)=>{
        if (err || !ret.ok) {
          RMSrv.dialogAlert('Failed to save manual estimates');
          return
        }
        RMSrv.dialogAlert('Manual estimates saved successfully');
      });
    },
    // TODO:重构代码
    updateProp(opt={}) {
      var self = this;
      var data = {
        _id:this.prop._id,
        status:this.prop.status_en
      };
      if (opt.phodl) {
        data.phodl = opt.phodl
      }
      if (opt.hideSqft != undefined) {
        data.hideSqft = opt.hideSqft
      }
      if (opt.hideBuiltYr != undefined) {
        data.hideBuiltYr = opt.hideBuiltYr
      }
      if (opt.rmPho){
        data.rmPho = this.prop.rmPho
        data.src = this.prop.src
      } 
      if (opt.maxPhoDownload){
        data.maxPhoDownload = true
        data.sid = this.prop.sid
        data.src = this.prop.src
      } 
      if (opt.priceChange) {
        data.priceChange = this.priceChange || false;
        delete data.status;
      }
      if (opt.daddr) {
        data.daddr = self.prop.daddr;
        delete data.status;
      }
      if (opt.rmdpho) {
        data.rmdpho = self.prop.rmdpho;
        delete data.status;
      }
      if (opt.dpred) {
        data.dpred = self.prop.dpred;
        delete data.status;
      }
      if (opt.removeTop) {
        data.removeTop = 1;
        data.ts = this.prop.ts;
        delete data.status;
      }
      if (opt.predFlw) {
        data.predFlw = !self.prop.predFlw;
      }
      if (opt.useMlatlng) {
        data.useMlatlng = !self.prop.useMlatlng;
      }
      // if (opt.refreshPic) {
      //   data.refreshPic = !self.prop.refreshPic;
      // }
      if (opt.gr) {
        data.tgr = self.prop.tgr;
        data.park_spcs = self.prop.park_spcs;
      }
      // console.log(opt);
      // return;
      if ((opt.yturl !== null) || (opt.ytvid !== null) || (opt.vurlcn !== null)) {
        data.ytvid = opt.ytvid;
        data.yturl = opt.yturl;
        data.vurlcn = opt.vurlcn;
        data.vimgcn = this.prop.vimgcn;
        var tmpvid = this.extractVidFromUrl(data.yturl);
        if (data.yturl && (tmpvid && data.ytvid!==tmpvid)) {
          data.ytvid = tmpvid;
          self.prop.ytvid = data.ytvid;
        }
        for (let i of ['ytvid','yturl','vurlcn','vimgcn']) {
          if (data[i]==='') {
            data.clearVid = 1;
          }
        }
      }
      if (opt.rmSqft){
        data.rmSqft = parseInt(opt.rmSqft);
      }
      if (opt.rmBltYr){
        data.rmBltYr = parseInt(opt.rmBltYr);
      }
      self.$http.post('/1.5/prop/manage', data).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            // self.prop = Object.assign({},self.prop,ret);
            if (opt.hideSqft != undefined) {
              self.prop.hideSqft = opt.hideSqft;
            }
            if (opt.hideBuiltYr != undefined) {
              self.prop.hideBuiltYr = opt.hideBuiltYr;
            }
            RMSrv.dialogAlert(JSON.stringify(ret));
            // window.bus.$emit('flash-message','Saved')
          } else {
            if (opt.hideSqft != undefined) {
              self.prop.hideSqft = !opt.hideSqft;
            }
            if (opt.hideBuiltYr != undefined) {
              self.prop.hideBuiltYr = !opt.hideBuiltYr;
            }
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    initGmap () {
      var self = this;
      var address, geocoder, lat, ll, lng, map, marker, opts;
      lat = 43.7182412;
      lng = -79.378058;
      ll = void 0;
      map = void 0;
      marker = void 0;
      opts = void 0;
      geocoder = void 0;
      address = "Mississauga, ON, Canada";
      if (self.prop.city != null) {
        address = self.prop.addr+', '+self.prop.city+', '+self.prop.prov;
      }
      ll = new google.maps.LatLng(lat, lng);
      opts = {
        zoom: 12,
        center: ll,
        mapTypeControl: true,
        mapTypeControlOptions: {
          style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
        },
        navigationControl: true,
        mapTypeId: google.maps.MapTypeId.ROADMAP
      };
      map = new google.maps.Map(document.getElementById("id_d_map"), opts);
      window.map = map;
      geocoder = new google.maps.Geocoder();
      if (geocoder) {
        return geocoder.geocode({
          address: address
        }, function(results, status) {
          var geocodePosition, infowindow;
          geocodePosition = function(pos) {
            return geocoder.geocode({
              latLng: pos
            }, function(responses) {
              if (responses && responses.length > 0) {
                return self.processGAddr(responses[0]);
              } else {
                return console.log("Cannot determine address at this location.");
              }
            });
          };
          if (status === google.maps.GeocoderStatus.OK) {
            if (status !== google.maps.GeocoderStatus.ZERO_RESULTS) {
              map.setCenter(results[0].geometry.location);
              infowindow = new google.maps.InfoWindow({
                content: "<b>" + address + "</b>",
                size: new google.maps.Size(150, 50)
              });
              marker = new google.maps.Marker({
                position: results[0].geometry.location,
                map: map,
                draggable: true,
                animation: google.maps.Animation.DROP,
                title: address,
                optimized: false
              });
              marker.addListener("click", function() {
                if (marker.getAnimation() !== null) {
                  return marker.setAnimation(null);
                } else {
                  return marker.setAnimation(google.maps.Animation.BOUNCE);
                }
              });
              map.addListener('click', function(e) {
                // placeMarkerAndPanTo(e.latLng, map);
                marker.setPosition(e.latLng);
                map.panTo(e.latLng);
                geocodePosition(marker.getPosition());
              });
              return google.maps.event.addListener(marker, "dragend", function() {
                return geocodePosition(marker.getPosition());
              });
            } else {
              return RMSrv.dialogAlert("No results found");
            }
          } else {
            return RMSrv.dialogAlert("Geocode was not successful for the following reason: " + status);
          }
        });
      }
    },
    processGAddr(place={}){
      var self = this, ref, ref1;
      self.prop.lat = typeof ((ref = place.geometry) != null ? (ref1 = ref.location) != null ? ref1.lat : void 0 : void 0) === 'function' ? place.geometry.location.lat() : place.geometry.location.lat;
      self.prop.lng = typeof ((ref = place.geometry) != null ? (ref1 = ref.location) != null ? ref1.lng : void 0 : void 0) === 'function' ? place.geometry.location.lng() : place.geometry.location.lng;
      // console.log(place);
      for (let part of place.address_components) {
        let type = part.types[0];
        let nm = part.short_name;
        let ln = part.long_name;
        if (type === "street_number") {
          self.prop['st_num'] = nm || '';
        } else if (type === "route") {
          self.prop['st'] = nm;
        } else if (type === "neighborhood") {
          self.prop['cmty'] = nm;
        } else if (type === "locality") {
          self.prop['city'] = nm;
        } else if (type === "administrative_area_level_2") {
        } else if (type === "administrative_area_level_1") {
          self.prop['prov'] = ln;
        } else if (type === "country") {
          self.prop['cnty'] = ln;
        } else if ((type === "postal_code") || (type === "postal_code_prefix")) {
          self.prop['zip'] = nm.replace(/\s+/g, '');
        } else {
          console.log(type);
        }
      }
      self.prop = Object.assign({},self.prop);
    },
    closeEdit(){
      window.location = '/1.5/index';
    },
    showVipInfo(id){
      let self = this;
      self.$http.post('/1.5/props/vipinfo', {id:id,editPropFlag:true}).then(
        function (ret) {
          if (ret.ok) {
            self.prop = Object.assign(self.prop,ret.data);
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
  }
}
</script>
