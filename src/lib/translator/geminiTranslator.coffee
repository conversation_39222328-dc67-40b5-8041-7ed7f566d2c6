{ GoogleGenerativeAI } = require ("@google/generative-ai")
debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class GeminiTranslator extends AITranslator
  constructor: (apiKey,model,prompt,maxUsage) ->
    super(apiKey,null,model,prompt,maxUsage)
    @genAI = new GoogleGenerativeAI(@apiKey)
    @modelGemini = @genAI.getGenerativeModel({model:@model})

  translate: (message, fromLang="English", toLang="Chinese") ->
    try
      @use()
      result = await @modelGemini.generateContent("#{@prompt} #{fromLang} to #{toLang}: #{message}" )
      response = await result.response
      debug.debug response

      translatedContent = response.text()
    
      if translatedContent.startsWith("Error")  # Replace this condition as needed
        throw new Error translatedContent
      
      translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
      return translatedContentDeleteN


      # translatedContent = response.text()
      # if response.ok
      #   return translatedContent
      # else
      #   throw new Error translatedContent
    catch error
      debug.error "Gemini Translation API Error:", error.message
      throw error
    finally
      @release()

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选，Gemini会将其合并到用户提示词中）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    # 构建完整的提示词，如果有系统提示词则合并
    fullPrompt = if systemPrompt then "#{systemPrompt}\n\n#{customPrompt}" else customPrompt

    try
      @use()
      result = await @modelGemini.generateContent(fullPrompt)
      response = await result.response
      debug.debug response

      translatedContent = response.text()

      if translatedContent.startsWith("Error")
        throw new Error translatedContent

      translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
      return translatedContentDeleteN

    catch error
      debug.error "Gemini Custom Prompt Translation API Error:", error.message
      throw error
    finally
      @release()

module.exports = GeminiTranslator