debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class RMTranslator extends AITranslator
  constructor: (endpoint,model,prompt,maxUsage) ->
    super(null,endpoint,model,prompt,maxUsage)
    
  translate: (message, fromLang="English", toLang="Chinese") ->
    data = {
      model: @model
      prompt: "#{@prompt} #{fromLang} to #{toLang}, Use the following translations for specific place names: City of Toronto → 多伦多市, Old Toronto → 旧多伦多市, East York → 东约克, Etobicoke → 怡陶碧谷, Scarborough → 士嘉堡, North York → 北约克, York → 约克, Mississauga → 密西沙加, Brampton → 布兰普顿, Markham → 万锦, Vaughan → 旺市, Richmond Hill → 列治文山, Oakville → 奥克维尔, Burlington → 伯灵顿, Pickering → 皮克灵, Ajax → 阿贾克斯, Whitby → 惠特比, Oshawa → 奥沙瓦, Clarington → 克拉灵顿, Uxbridge → 厄克斯桥, Scugog → 斯库高, Brock → 布鲁克, Newmarket → 新市, Aurora → 奥罗拉, King → 金, East Gwillimbury → 东贵林, Georgina → 乔治娜, Whitchurch-Stouffville → 圣维尔, Milton → 米尔顿, Halton Hills → 荷顿山, Caledon → 卡利登, U of T Scarborough Campus → 多伦多大学士嘉堡校区. Do not translate other place names. Only return the translated text: #{message}"
      stream: false
    }

    options = {
      errorPrefix: 'RM Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        if result.data.response?
          translatedContent = result.data.response
          translatedContentDeleteN = translatedContent.replace(/\n/g, '')
          return translatedContentDeleteN
        else
          throw new Error('Empty response from translation API')
      else
        throw new Error("HTTP error! status: #{result.response.status}")
    catch error
      debug.error 'RM Translation API Error:', {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: result?.data or 'No response parsed'
      }
      throw error

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选，RM模型会将其合并到用户提示词中）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    # 构建完整的提示词，如果有系统提示词则合并
    fullPrompt = if systemPrompt then "#{systemPrompt}\n\n#{customPrompt}" else customPrompt

    data = {
      model: @model
      prompt: fullPrompt
      stream: false
    }

    options = {
      errorPrefix: 'RM Custom Prompt Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        if result.data.response?
          translatedContent = result.data.response
          translatedContentDeleteN = translatedContent.replace(/\n/g, '')
          return translatedContentDeleteN
        else
          throw new Error('Empty response from translation API')
      else
        throw new Error("HTTP error! status: #{result.response.status}")
    catch error
      debug.error 'RM Custom Prompt Translation API Error:', {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: result?.data or 'No response parsed'
      }
      throw error

module.exports = RMTranslator