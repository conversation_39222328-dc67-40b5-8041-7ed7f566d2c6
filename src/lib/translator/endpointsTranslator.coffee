debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'

class EndpointsTranslator extends AITranslator
  constructor: (apiKey, endpoint, model, prompt, maxUsage) ->
    super(apiKey, endpoint, model, prompt, maxUsage)
    
  translate: (message, fromLang='English', toLang='Chinese') ->
    data = {
      model: @model
      messages: [
        { role: 'user', content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
      stream: false
    }

    options = {
      headers: {
        'Authorization': "Bearer #{@apiKey}"
      }
      timeout: 30000
      errorPrefix: 'Endpoints Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        # 支持多种响应格式
        if result.data.choices?[0]?.message?.content
          translatedContent = result.data.choices[0].message.content
        else if result.data.response?
          # 兼容其他格式的响应
          translatedContent = result.data.response
        else
          throw new Error('Empty or invalid response from Endpoints translation API')

        # 清理换行符
        translatedContentCleaned = translatedContent.replace(/\n+$/, '')
        return translatedContentCleaned
      else
        throw new Error("HTTP error! status: #{result.response.status}")
    catch error
      debug.error 'Endpoints Translation API Error:', {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: result?.data or 'No response parsed'
      }
      throw error

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    messages = []

    # 添加系统提示词（如果提供）
    if systemPrompt
      messages.push { role: 'system', content: systemPrompt }

    # 添加用户提示词
    messages.push { role: 'user', content: customPrompt }

    data = {
      model: @model
      messages: messages
      stream: false
    }

    options = {
      headers: {
        'Authorization': "Bearer #{@apiKey}"
      }
      timeout: 30000
      errorPrefix: 'Endpoints Custom Prompt Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        # 支持多种响应格式
        if result.data.choices?[0]?.message?.content
          translatedContent = result.data.choices[0].message.content
        else if result.data.response?
          # 兼容其他格式的响应
          translatedContent = result.data.response
        else
          throw new Error('Empty or invalid response from Endpoints translation API')

        # 对于自定义提示词，不进行长度检查，因为可能是过滤等其他用途
        translatedContentCleaned = translatedContent.replace(/\n+$/, '')
        return translatedContentCleaned
      else
        throw new Error("HTTP error! status: #{result.response.status}")
    catch error
      debug.error 'Endpoints Custom Prompt Translation API Error:', {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: result?.data or 'No response parsed'
      }
      throw error

module.exports = EndpointsTranslator
