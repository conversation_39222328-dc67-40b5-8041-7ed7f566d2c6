debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'
ERR_THRESHOLD=0.2

class DeepSeekTranslator extends AITranslator
  constructor: (apiKey,endpoint,model,prompt,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    

  translate: (message, fromLang="English", toLang="Chinese") ->
    data = {
      model: @model
      messages: [
        { role: "user", content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
      stream: false
    }

    options = {
      headers: {
        'Authorization': "Bearer #{@apiKey}"
      }
      timeout: 30000
      errorPrefix: 'DeepSeek Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        translatedContent = result.data.choices[0].message.content
        # check if the return is too short
        threshold = Math.ceil(message.length * ERR_THRESHOLD)
        if translatedContent.length < threshold
          debug.error 'Translation result is too short', translatedContent
          # 不用throw，直接返回空字符串
          return ''
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        err = new Error(result.data.error.message)
        err.name = result.data.error.type
        throw err
    catch error
      if error.name is 'TimeoutError'
        debug.error 'Timeout: It took more than 30 seconds to get the result!'
      if error.name is 'authentication_error'
        debug.error 'DeepSeek Translation API Authentication Error, check key'
      else
        debug.error 'DeepSeek Translation API Error:', error.message
      throw error

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    messages = []

    # 添加系统提示词（如果提供）
    if systemPrompt
      messages.push { role: 'system', content: systemPrompt }

    # 添加用户提示词
    messages.push { role: 'user', content: customPrompt }

    data = {
      model: @model
      messages: messages
      stream: false
    }

    options = {
      headers: {
        'Authorization': "Bearer #{@apiKey}"
      }
      timeout: 30000
      errorPrefix: 'DeepSeek Custom Prompt Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        translatedContent = result.data.choices[0].message.content
        # 对于自定义提示词，不进行长度检查，因为可能是过滤等其他用途
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        err = new Error(result.data.error.message)
        err.name = result.data.error.type
        throw err
    catch error
      if error.name is 'TimeoutError'
        debug.error 'Timeout: It took more than 30 seconds to get the result!'
      if error.name is 'authentication_error'
        debug.error 'DeepSeek Custom Prompt Translation API Authentication Error, check key'
      else
        debug.error 'DeepSeek Custom Prompt Translation API Error:', error.message
      throw error

module.exports = DeepSeekTranslator