debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class ClaudeTranslator extends AITranslator
  constructor: (apiKey,endpoint,model,prompt,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    
  translate: (message, fromLang="English", toLang="Chinese") ->
    data = {
      model: @model
      max_tokens: 1024
      messages: [
        { role: 'user', content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
    }

    options = {
      headers: {
        'x-api-key': "#{@apiKey}"
        'anthropic-version': '2023-06-01'
      }
      errorPrefix: 'Claude Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        translatedContent = result.data.content[0].text
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        throw new Error(result.data.message)
    catch error
      debug.error 'Claude Translation API Error:', error.message
      throw error

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    messages = []

    # Claude支持系统消息
    if systemPrompt
      messages.push { role: 'system', content: systemPrompt }

    # 添加用户提示词
    messages.push { role: 'user', content: customPrompt }

    data = {
      model: @model
      max_tokens: 1024
      messages: messages
    }

    options = {
      headers: {
        'x-api-key': "#{@apiKey}"
        'anthropic-version': '2023-06-01'
      }
      errorPrefix: 'Claude Custom Prompt Translation API Error'
    }

    try
      result = await @invokeFetch(data, options)

      if result.success
        translatedContent = result.data.content[0].text
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        throw new Error(result.data.message)
    catch error
      debug.error 'Claude Custom Prompt Translation API Error:', error.message
      throw error

module.exports = ClaudeTranslator