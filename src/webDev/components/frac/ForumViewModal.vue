<template lang="pug">
div#forumViewModal.modal
  confirm-with-message(:on-confirm='deleteMethod',:parent-name="parentName")
  share-dialog(:w-dl.sync="wDl", :w-sign.sync="wSign", :disp-var="dispVar", :height="160", :prop="prop", :no-lang="true")
  div.W<PERSON>ridge(style="display:none")
    forum-share-desc(:post.sync="post", :is-app="dispVar.isApp", :req-host="dispVar.shareHost",:w-id="wId")
    div#share-url {{shareUrl}}
  header.bar.bar-nav(v-if="!nobar")
    //- span.icon.icon-left.pull-left(@click="goTo('/1.5/index')",v-show="!noBack")
    span.icon.icon-close.pull-right(v-if="!post.gid || post.readStatus=='read'|| share",@click="closeForumView()")
    div.pull-right.read-status(v-else)
      span.btn.btn-primary(@click="changeStatus('later')", style="top:2px;") {{_('Unread','forum')}}
      span.icon.icon-close(@click="changeStatus('read')")
    h2.title {{_('RealMaster')}}
  flash-message
  div.mask(v-show="showMask", @click="close()")
  div.mask.nickname-mask(v-show="showNickNameMask", @click="closeNickName()")
  div.readers(v-if="showReaders")
    div(style="width: 100%;height: 40px;")
    div.reader-wrapper.header
      span.nm {{_('Name','Forum')}}
      span.status {{_('Status','Forum')}}
      span.time {{_('Time','Forum')}}
    div.reader-wrapper(v-for="reader in groupMembers")
      span.nm {{reader.nm_zh}} {{reader.nm_en}}
      span.status {{reader.status}}
      span.time {{formatTs(reader.readts)}}
    div.bar.bar-standard.bar-footer.footer-tab
      span.btn.btn-primary.btn-half(@click="showReaders=false") {{_('Close')}}
      span.btn.btn-primary.btn-half(v-if="computedAdmin && post.gid", @click="pushPost('unread')")
        | {{_('Push to Unread')}}
  create-nickname(:on-confirm="saveNickName", :on-cancel="cancelNickName", :is-admin="computedAdmin")
  div#postViewContainer.content
    div(style="top: 50%;position:absolute;left:50%; z-index: 1000;")
      div.pull-spinner(v-show="loading", style='display:block')
    div.post-top-container(v-if="post.adTopPhoto")
      img(:src="post.adTopPhoto", @error="setDefaultPhoto",@click="openAd(post.adTop,0)", referrerpolicy="same-origin")
      div.post-top-text {{_('AD','forum')}}
        span(v-if="dispVar.forumAdmin") {{post.vcad0}}
    span.realtor-only(v-if="post.realtorOnly")
      | {{_('Realtor Only','forum')}}
    span.realtor-only(v-if="post.gid && post.gnm", style="font-size:14px; padding: 2px 15px;")
      | {{post.gnm}}
    div(v-if="post.src!='news'")
      h3.post-title(v-bind:class="{cross: post.del}")
        span(@click="goToProperty()")
          span(v-if="post.src=='property' && post.cmntl") {{post.cmntl + ' ' + post.tl}}
          span(v-else) {{post.tl}}
      div.post-author(v-if="post.fornm")
        div.img.post-img(@click="showAuthorInfo(post.uid)",:src="post.avt",:style="{ 'background-image': getImageUrl(post.avt)}" )
        div(style="float:left; margin-top: -5px;")
          span.name(@click="showUsersPost()") {{post.fornm}}
          div.ts {{formatTs2(post.ts)}}
      pre(v-if="post.m")
        div.post-content(v-html="post.m")
    div(v-if="post.src=='news'")
      div.post-content(v-html="post.m || post.tl")
    div.post-photo(v-if="post.photos && post.photos.length && post.src!=='news'")
      div.img-wrapper(v-for="photo in post.photos", track-by="$index")
        img(:src="photo", @error="setDefaultPhoto", :data-index="$index", @load="setUpPreview($event)", @click="previewPic($event)", referrerpolicy="same-origin")
        span.search(v-if="showSearchIcon", @click='goToProperty()')
          span.details {{_('Details','forum')}}
          span.fa.fa-search-plus
    div.post-top-container(v-if="post.adBottomPhoto" style="margin-top:20px;")
      img(:src="post.adBottomPhoto", @error="setDefaultPhoto",@click="openAd(post.adBottom,1)", referrerpolicy="same-origin")
      div.ad-text {{_('AD','forum')}}
        span(v-if="dispVar.forumAdmin") {{post.vcad1}}
    div.post-tags(v-if="!post.del")
      span.btn.btn-default(v-if="post.city||post.prov||post.cnty") {{_(post.city||post.prov||post.cnty)}}
      span.btn.btn-default(v-for="tag in post.tags", @click="onTagClicked(tag)")
        span(v-if="tag=='HOT'") {{_('HOT','forum')}}
        span(v-else) {{tag}}
    div.post-tags.post-tags-admin(v-if="!post.del && computedAdmin")
      span.btn(v-if="post_sticky && post.sticky=='global' && computedAdmin", @click="sticky(false)")
        | {{_('unSticky')}}
      span.btn(v-if="post_sticky && post.sticky=='city' && computedAdmin", @click="sticky(false)")
        | {{_('unSticky')}}
      span.btn(v-if="post.status!=='new' && !post_sticky && computedAdmin", @click="sticky('global')")
        | {{_('Sticky')}}
      //- span.btn(v-if="!post_sticky && computedAdmin && post.city", @click="sticky('city')")
      //-   | {{_('Sticky')}}
      span.btn(v-if="computedAdmin", @click="pushPost()",:class='{disabled:pushNotified}')
        | {{_('Push Notify All')}}
        span(v-if="post_pn",style="padding-left:2px") (pn1)

      span.btn(v-if="!post.discmnt && computedAdmin", @click="disableCmnt(true)")
        | {{_('disableCmnt')}}
      span.btn(v-if="post.discmnt && computedAdmin", @click="disableCmnt(false)")
        |{{_('enableCmnt')}}
      span.btn(v-if="computedAdmin && !post.gid")
        input(type="checkbox",v-model='post.realtorOnly', @change="manageRealtorOnly()")
        span(style="padding-left:10px") {{_('RealtorOnly','forum')}}
      span.btn(v-if="computedAdmin", style="width: 280px;" @click="updateMt()")
        | {{_('Update Mt')}}
        span(style="padding-left:2px") ({{post.mt}})
      span.btn(v-if="computedAdmin && !post.gid", style="width: 280px;" @click="refreshWpCategory()")
        | {{_('Refresh wordpress tags')}}
      span.btn(v-if="computedAdmin && !post.gid", style="width: 280px;" @click="pushToWp()")
        | {{_('Push To wordPress')}}
        span(v-show="wpmt",style="padding-left:2px") ({{wpmt}})
      span.btn(v-if="post.gid && computedAdmin", style="width: 200px;" @click="showMembers()")
        | {{_('Who Read')}}
    div.post-tags.post-tags-admin(v-if="dispVar.edmAdmin")
      span.btn(style="width: 200px; margin-right:20px;" @click="manageEdm('edmw')") {{_('Weekly Edm')}}
        span(v-if="edmw",style="padding-left:2px") ({{edmw}})
      span.btn(style="width: 200px; margin-right:20px;" @click="manageEdm('edmd')") {{_('Daily Edm')}}
        span(v-if="edmd",style="padding-left:2px") ({{edmd}})
      span.btn(style="width: 200px; margin-right:20px;" @click="manageEdm('edm')") {{_('Whole Page Edm')}}
        span(v-if="edm",style="padding-left:2px") ({{edm}})

    div.signupform(v-if="post.showRequestInfo")
      sign-up-form(:user-form.sync="userForm", :title="post.requestInfotl", :owner="owner", :is-web="true", :need-wxid="true",:feedurl="feedurl")

    div.signupform(v-if="post.formid")
      app-form-preview-modal(:formid="post.formid", nobar=true, is-web=true, :forumid="post._id", :fortl="post.tl")

    div.post-topics(v-if="relatedPosts && relatedPosts.length")
      div.tp-title {{_("Topic","forum")}}: {{ post.tp ||relatedPosts[0].tp}}
      div(v-for="p in relatedPosts")
        div.topic-content(v-bind:class="{mainTopic: p.tp}", @click="openRelatedPost(p._id)")
          img(v-if="p.thumb",:src="p.thumb", @error="setDefaultPhoto", referrerpolicy="same-origin")
          div.line-wrap.line-wrap-2.content-right(v-if="p.thumb") {{p.tl}}
          div.line-wrap.line-wrap-2.full-width(v-else) {{p.tl}}
    div.post-topics(v-show="wecards && wecards.length")
      div.tp-title {{_("Agent Recommended","forum")}}
      div(v-for="p in wecards")
        div.topic-content(@click="openWecard(p)")
          img(v-if="p.meta.img",:src="p.meta.img || '/img/logo.png'", @error="setDefaultPhoto", referrerpolicy="same-origin")
          div.content-right
            div.line-wrap.line-wrap-1 {{p.meta.title}}
            div.line-wrap.line-wrap-1.font14(style="color: #777;")
              | {{p.meta.desc}}
    div.post-topics(v-show="recommeds && recommeds.length")
      div.tp-title {{_("RealMaster Hot Forum Articles","forum")}}
      div(v-for="p in recommeds")
        div.topic-content(@click="openRelatedPost(p._id)")
          img(v-if="p.thumb",:src="p.thumb", @error="setDefaultPhoto", referrerpolicy="same-origin")
          div.line-wrap.line-wrap-2.content-right(v-if="p.thumb") {{p.tl}}
          div.line-wrap.line-wrap-2.full-width(v-else) {{p.tl}}
    a.download-button(v-if="wId", href="/app-download") {{_('Read More Posts')}}

    div(v-if="wct")
      div.comments-header(v-show="!post.discmnt && recent_cmnts.length")
        span(style="color:#E03131") {{_('Comments','forum')}}
        span.pull-right.font-size-15(@click="reverseCmnt()") {{_('Time','forum')}}
          span.padding-left-5.pull-right.fa.fa-long-arrow-down(v-if="cmntSortOrder=='desc'")
          span.padding-left-5.pull-right.fa.fa-long-arrow-up(v-if="cmntSortOrder=='asc'")
      div#recent_cmnts_container(v-if="!post.discmnt")
        forum-cmnt-card(v-for="cmnt in display_cmnts", track-by="$index",:cmnt="cmnt", :can-edit="computedAdmin")
      div(style="text-align: center;", v-if="recent_cmnts.length>20 && display_cmnts.length < recent_cmnts.length",@click="showAllComments()")
        span(class='fa fa-arrow-down')
        div {{_('show More')}}
      div(style="text-align: center;", v-if="recent_cmnts.length>20 && display_cmnts.length == recent_cmnts.length",@click="CollapseComments()")
        span(class='fa fa-arrow-up')
        div {{_('Collapse')}}
    user-brief-info(v-if="wId && aId", :owner.sync="dispVar.ownerData", :qrcd.sync="qrcd")
  div(v-if="!wId")
    div.bar.bar-standard.bar-footer.footer-tab(v-bind:class="{wDlBottom:wDl}", v-bind:style="{height:cmntsBarFooterHeight+'px', bottom:cmntsBottomHeight+'px'}")
      div(v-if="canComment")
        textarea#commentTextarea(v-bind:style="{fontSize:cmntFontSize+'px', borderRadius: cmntsBorderRadius+'px',marginTop:cmntsMarginTop+'px', height:cmntsHeight+'px', width:cmntsWidth+'%'}",v-model="comment", :placeholder="_('Write Comments...')",@click="showComments()")
      div(v-if="post.status!='new'", style="vertical-align:middle")
        a(v-if="!post.del && !wId", @click="showSMB()")
          span.icon.fa.fa-rmshare
          span.tab-label {{_('Share')}}
        a(v-if="!post.del && dispVar.isLoggedIn && post_fav", @click="favourite(false)")
          span.icon.fa-heart
          span.tab-label {{_('Favour','Save')}}
        a(v-if="!post.del && dispVar.isLoggedIn && !post_fav", @click="favourite(true)")
          span.icon.fa-heart-o
          span.tab-label {{_('Favour','Save')}}
        a(v-if="canComment",@click="onCommentsIconClick()")
          span.icon.fa.fa-chat
            span.comment-number {{computedCmntNum}}
          span.tab-label {{_('Comments','forum')}}
        a(v-if="editable()", @click="goToEdit()")
          span.icon.icon-edit
          span.tab-label {{_('Edit')}}
        a(v-if="computedAdmin && !post.del", style="color:black", @click="deletePostConfirm('delete')")
          span.icon.icon-trash.post-delete
          span.tab-label {{_('Delete')}}
        a.post-delete(v-if="computedAdmin && post.del", @click="deletePostConfirm('db_delete')")
          span.icon.icon-trash
          span.tab-label {{_('DBDelete','forum')}}
        a(v-if="computedAdmin && post.del",  @click="deletePostConfirm('recover')")
          span.icon.fa.fa-undo.post-recover
          span.tab-label {{_('Recover')}}
    div.bar.bar-standard.bar-footer.comment-dialog-footer(v-bind:style="{bottom:cmntsPublishBottomHeight+'px'}", v-show="showCommentDialog")
      div.pull-left(style="padding: 10px 0px")
        div.img.post-img.no-margin-right(:src="dispVar.sessionUser.avt",:style="{ 'background-image': getImageUrl(dispVar.sessionUser.avt)}" )
      span.pull-left.nickname
        | {{dispVar.sessionUser.fornm}}
      span.pull-right(:disabled="publishing", @click="publishCmnt()")
        | {{_('Publish')}}
      span.pull-right(style="color:grey",@click="close()")
        | {{_('Cancel')}}
  get-app-bar(:dtl-in-app="true", :w-dl.sync="wDl", :owner.sync="dispVar.ownerData", :params="params", v-show="share")
  div.authorInfo(v-if="showAuthorDialog")
    span.icon.icon-close.pull-right(@click="closeAuthorInfo()")
    h3 {{_('Author Information','forum')}}
    div {{_('EMAIL','forum')}}: {{authorInfo.eml}}
    div {{_('Phone','forum')}}: {{authorInfo.mbl}}
    div {{_('Full Name','forum')}}: {{authorInfo.fn}}
    div {{_('Forum NickName','forum')}}: {{authorInfo.fornm}}
</template>

<script>
import FlashMessage from './FlashMessage'
import pagedata_mixins from '../../../coffee4client/components/pagedata_mixins'
import ConfirmWithMessage from './ConfirmWithMessage'
import ForumCmntCard from './ForumCmntCard'
import ShareDialog from './ShareDialog2.vue'
import forum_mixins from '../../../coffee4client/components/forum/forum_mixins'
import ForumShareDesc from './ForumShareDesc.vue'
import GetAppBar from './GetAppBar'
import CreateNickname from './CreateNickname.vue'
import rmsrv_mixins from '../../../coffee4client/components/rmsrv_mixins'
import UserBriefInfo from './UserBriefInfo.vue'
import SignUpForm from '../common/signUpForm.vue'
import AppFormPreviewModal from '../form/appFormPreviewModal.vue'
import LZString from 'lz-string'
import forum_common_mixins from '../../../coffee4client/components/forum/forum_common_mixins'

export default {
  mixins: [pagedata_mixins,forum_mixins,forum_common_mixins,rmsrv_mixins],
  data () {
    return {
      feedurl: '/1.5/user/contact',
      userForm : {forumid:null, forumtl: null},
      owner:{vip:1},
      datas:[
        'isCip',
        'isApp',
        'userCity',
        'isLoggedIn',
        'lang',
        'forumAdmin',
        'sessionUser',
        'shareHost',
        'newsAdmin',
        'canWebComment',
        'globalTags',
        'isVipRealtor',
        'isAdmin',
        'isRealtor',
        'edmAdmin',
        'sessionUser',
        'userGroups'
      ],
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        forumAdmin: false,
        sessionUser: {
          fas:[]
        },
        shareHost:false,
        canWebComment: false,
        globalTags:[],
        ownerData:{},
        isVipRealtor: false,
        isAdmin:false,
        isRealtor: false,
        edmAdmin: false,
        userGroups:[],
      },
      qrcd: false,
      post:{
        del:null,
        tl:'',
        m:'',
        sticky:false,
        cmnts:[],
        favs:[],
        city:null,
        prov:null,
        avt:null,
        photos:[],
        wpHosts:[]
      },
      comment:"",
      showCommentDialog:false,
      referIdx: undefined,
      parentName: 'forumView',
      recent_cmnts: [],
      display_cmnts:[],
      sticky_cmnts: [],
      relatedPosts: [],
      post_sticky: null,
      post_fav: null,
      authorIsVip:false,
      wating:false,
      cmntPageCnt: 20,
      cmntTotalPageCnt: 100,
      wDl: false,
      wSign: true,
      wComment: true,
      wId: false,
      loading: false,
      waiting:false,
      showMask: false,
      deleteMethod: null,
      // noBack: true,
      prop:{},
      cmntsWidth:40,
      cmntsHeight:30,
      cmntsMarginTop:10,
      cmntsBorderRadius:50,
      cmntsBarFooterHeight:50,
      cmntSortOrder:'desc',
      cmntSortKey: 'ts',
      cmntFontSize:10,
      section:null,
      city:null,
      aId: false,
      showNickNameMask: false,
      nobar:vars.nobar||0,
      cmntsBottomHeight:0,
      cmntsPublishBottomHeight:0,
      showAuthorDialog: false,
      authorInfo:{},
      publishing: false,
      post_pn: 0,
      sizes:[],
      picUrls:[],
      edm: null,
      edmd: null,
      edmw: null,
      wct: true,
      recommeds: [],
      wecards:[],
      userid:'',
      showReaders:false,
      groupMembers:[],
      share:vars.share,
      wpmt: null,
      pushNotified:false,
    };
  },
  computed:{
    showSearchIcon() {
      return this.post.src=='property' && vars.src!=='property'
    },
    computedCmntNum() {
      if (!this.post.cmnts)
        return 0;
      var cmnts = this.post.cmnts.filter(function(cmnt){
        return !cmnt.del;
      });
      return cmnts.length > 99 ? '99+' :  cmnts.length;
    },
    shareUrl: function () {
      if (this.wId)
        return document.location.href;
      var url = this.dispVar.shareHost + '/1.5/topics/details?share=1&id=' + this.post._id + '&lang=' + this.dispVar.lang;
      if (this.post.gid)
        url += '&gid='+this.post.gid ;
      if (!this.wComment)
        url += '&wct=0';
      if (this.dispVar.isRealtor && this.wSign) {
        url += '&aid=' + this.dispVar.sessionUser._id;
      }
      return url;
    },
    canComment: function() {
      return !this.post.discmnt && !this.post.del && (!this.post.cc || this.post.cc < 1000)
    }
  },
  methods: {
    refreshWpCategory() {
      if (this.loading == true)
        return
      this.loading = true;
      this.$http.post('/1.5/forum/refreshWpCategory',{}).then(
        function(ret) {
          if (ret.data.ok) {
            window.bus.$emit('flash-message', "refreshed");
          } else {
            window.bus.$emit('flash-message', ret.data.e);
          }
          this.loading = false;
        },
        function(err) {
          this.loading = false;
          ajaxError(err);
        }
      )
    },
    showUsersPost() {
      if (!this.post.category) {
        this.goTo('/1.5/forum/list?author='+post.uid)
      }
    },
    showMembers() {
      var self = this;
      if (self.loading == true) {
        return;
      }
      self.$http.post('/group/detail',{id:self.post.gid}).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 100);
          if(ret.ok) {
            var group = ret.group;
            self.groupMembers = group.members || [];
            if (self.post.readers && self.post.readers.length) {
              for (let member of self.groupMembers) {
                  var u = self.post.readers.find(function(reader){
                    return reader.uid.toString() == member.uid.toString();
                  });
                  if(u) {
                    member.status = u.status;
                    member.readts = u.mt || u.ts;
                  }
              }
            };
            this.showReaders = true;
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          self.loading = false;
          ajaxError(err);
        }
      )
    },
    changeStatus(status) {
      var self = this;
      this.$http.post('/1.5/forum/changeStatus',{id: this.post._id, status:status, gid:this.post.gid}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.readStatus = status;
            self.closeForumView();
          }
        },
        function(err) {
          ajaxError(err);
        });
    },
    openWecard(card) {
      var url=""
      if(card.meta.prop) {
        url = '/1.5/prop/detail?id='+card.meta.id+'&wDl=1&sgn=1&lang=zh-cn'+'&aid='+vars.aid;
      } else {
        url = '/1.5/wecard/prop/'+ this.userid + '/' + card._id + '?wDl=1&sgn=1';
      }
      window.location.href=url;
    },
    openAd(url, index) {
      this.$http.post('/1.5/forum/adClick',{id: this.post._id, index:index, gid:this.gid}).then(
        function(ret) {
          if (ret.data.ok) {
            if (!url) {
              return null;
            } else {
              RMSrv.showInBrowser(url);
            }
          }
        },
        function(err) {
          ajaxError(err);
        });
    },
    manageRealtorOnly() {
      var self = this;
      var params = {type:'realtorOnly',realtorOnly: self.post.realtorOnly, gid: self.post.gid}
      self.$http.post('/1.5/forum/sticky/'+self.post._id, params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            return;
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (err) {
          ajaxError(err);
        }
      );
    },
    manageEdm(type) {
      var self = this;
      var url = '/1.5/forum/edm';
      var params = {_id: self.post._id}
      params.del = this.post[type] ? 1 : 0;
      params.type = type;
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post(url, params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            if (ret[type]) {
              self.post[type] = ret[type];
              self[type] =  self.formatTs(ret[type]);
            } else {
              self.post[type] = null
              self[type] = null;
            }
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (err) {
          ajaxError(err);
        }
      );
    },
    setUpPreview (evt) {
      var h, w, self=this, el=evt.target;
      w = el.naturalWidth;
      h = el.naturalHeight;
      if ( w>0 && h>0 ) {
        self.sizes.push(w + 'x' + h);
        el.setAttribute('data-size', w + 'x' + h);
      }
    },
    openTBrowser (url, cfg={}) {
      if (this.dispVar.isApp) {
        this.tbrowser(url);
      } else {
        window.location.href = url;
      }
    },
    previewPic (evt) {
      var self = this;
      self.picUrls = [];
      self.sizes = [];
      var compressed, index, text, url, el=evt.target;
      var imgs = document.querySelectorAll('.post-content img, .post-photo img')||[]
      for (var img of imgs) {
        var datasize = img.getAttribute('data-size');
        if (datasize) {
          self.sizes.push(datasize);
          self.picUrls.push(img.src);
        }
      }
      index = self.picUrls.findIndex(function(pic) {
        return pic == el.src
      });
      if (!index) {
        index = Math.abs(window.slideNumber) || 0;
      }
      text = JSON.stringify({
        urls: self.picUrls,
        index: index,
        sizes: self.sizes
      });
      if (!LZString) {
        return console.error('LZString is required!');
      }
      compressed = LZString.compressToEncodedURIComponent(text);
      url =  '/1.5/SV/photoSwipe?data=' + compressed;
      url = this.appendDomain(url);
      if (!window.RMSrv && !this.inFrame) {
        return;
      }
      self.openTBrowser(url);
    },
    showAuthorInfo(uid) {
      if (this.post.category && vars.from!='wesite' && this.post.isMerchant) {
        return this.openTBrowser(this.appendDomain('/1.5/wesite/'+uid));
      }
      if(!this.dispVar.isAdmin) return;
      var self = this;
      self.$http.get('/1.5/forum/authorInfo?uid='+uid).then(
        function(ret) {
          if (ret.data.ok) {
            self.authorInfo = ret.data.user;
            self.showAuthorDialog = true;
            self.showMask = true;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    closeAuthorInfo() {
      this.showMask = false;
      this.showAuthorDialog = false;
    },
    closeNickName() {
      window.bus.$emit('close-edit-nickname');
    },
    onCommentsIconClick() {
      if(this.post.cmnts.length) {
        document.getElementById('recent_cmnts_container').scrollIntoView()
      } else {
        this.showComments();
      }
    },
    openRelatedPost(id) {
      if (vars.id)
        this.goTo(this.appendDomain('/1.5/topics/details?id='+id));
      else
       this.closeForumView(id);
    },
    goToEdit() {
      var city;
      var url,backurl;
      if (this.section== 'yellowpage') {
        backurl = '/1.5/yellowpage?1=1'
      } else if (this.section =='reno') {
        backurl = '/1.5/staging?1=1'
      } else if (this.section) {
        backurl = "/1.5/forum?section="+this.section;
      } else if (this.city) {
        backurl = "/1.5/forum?city="+this.city;
      }
      if (backurl) {
        backurl = backurl + '&postid='+this.post._id;
      }
      if(this.post.gid) {
        backurl = backurl + '&gid='+this.post.gid;
        url = '/1.5/forum/edit?id='+this.post._id+'&gid='+this.post.gid
      } else {
        url = '/1.5/forum/edit?id='+this.post._id
      }

      if (backurl) {
        url =url+'&d='+encodeURIComponent(backurl);
      }
      this.goTo(url);
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    goToProperty() {
      if (vars.id && vars.src === 'property')
        return;
      if(this.post.src==='property') {
        var url = '/1.5/prop/detail?id='+ this.post._id+'&inframe=1&nobar=1';
        url = this.appendDomain(url);
        if (this.dispVar.isApp) {
          this.tbrowser(url);
        } else {
          window.location.href = url;
        }
      }
    },
    sortCmnt(cmnt1, cmnt2) {
      if (cmnt1.sticky && !cmnt2.sticky) return -1;
      if (!cmnt1.sticky && cmnt2.sticky) return 1;
      if (cmnt1.sticky && cmnt2.sticky)
        return (new Date(cmnt2.mt) - new Date(cmnt1.mt))
      if (this.cmntSortKey === 'ts') {
        if (this.cmntSortOrder === 'desc')
          return (new Date(cmnt2.ts) - new Date(cmnt1.ts))
        if (this.cmntSortOrder === 'asc')
          return (new Date(cmnt1.ts) - new Date(cmnt2.ts))
      }
    },
    reverseCmnt() {
      if (this.cmntSortOrder =='desc')
        this.cmntSortOrder ='asc'
      else
        this.cmntSortOrder ='desc'
      this.recent_cmnts.sort(this.sortCmnt);
      this.display_cmnts = this.recent_cmnts.slice(0,19);
    },
    showAllComments() {
      this.display_cmnts  = this.recent_cmnts;
    },
    CollapseComments() {
      this.display_cmnts = this.recent_cmnts.slice(0,19);
    },

    disableCmnt(disable) {
      var self = this;
      var _doDisableCmnt = function(idx) {
        if (idx+'' != '2') {
          return;
        }
        var params = {discmnt:disable, city: self.post.city, prov: self.post.prov}
        if(self.post.gid) {
          params.gid = self.post.gid
        }
        self.$http.post('/1.5/forum/disableCmnt/' + self.post._id, params).then(
            function(ret) {
              if (ret.data.ok) {
                self.post.discmnt = disable;
              } else {
                return window.bus.$emit('flash-message', ret.data.e);
              }
            },
            function(err) {
              ajaxError(err);
            }
          );
      };
      var text;
      disable? text = 'disable' : text = 'enable'
      var msg = 'Are you sure to '+ text +' the comments?'
      RMSrv.dialogConfirm(msg,
        _doDisableCmnt,
        "Message",
        ['Cancel', 'Confirm']
      );
    },
    pushPost(unread) {
      var self = this;
      self.pushNotified = true;
      window.bus.$emit('flash-message', "Push Notified");
      var params = {id: self.post._id, tl: self.post.tl, realtorOnly:self.post.realtorOnly};
      if (unread)
        params.groupUnread = unread;
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/pushNotify/', params).then(
        function(ret) {
          if (ret.data.ok) {
            self.post_pn = 1;
            return window.bus.$emit('flash-message', ret.data.msg);
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    updateMt() {
      var self = this;
      var params = {id: self.post._id};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/updateMt/', params).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.mt = ret.data.mt;
            return window.bus.$emit('flash-message', "Updated");
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    pushToWp() {
      var self = this;
      if(self.loading == true) {
        return;
      }
      self.loading = true;
      self.$http.post('/1.5/forum/pushToWp', {post: this.post}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.wpHosts = ret.data.wpHosts;
            self.post.wpHosts = self.post.wpHosts.slice(0);
            self.wpmt = self.post.wpHosts[0].mt;
            window.bus.$emit('flash-message', "Pushed");
          } else {
            window.bus.$emit('flash-message', ret.data.e);
          }
          self.loading = false;
        },
        function(err) {
          self.loading = false;
          ajaxError(err);
        }
      );
    },
    goTo(url) {
      window.location.href=url;
    },
    setDefaultPhoto(event) {
      if(/m.i.realmaster.com|img.realmaster.com/i.test(event.srcElement.src)) {
        event.onerror=null;
        event.srcElement.src='/img/no-pic.png';
      } else if(/img.realmaster.cn/i.test(event.srcElement.src)) {
        event.srcElement.src=event.srcElement.src.replace('img.realmaster.cn','m.i.realmaster.com')
      } else {
          event.srcElement.src='/img/no-pic.png';
      }
    },
    onTagClicked(tag) {
      if (!this.wId) {
        var element = this.dispVar.globalTags.find(function(element){return element.key == tag;});
        if(this.displayPage === 'all' && !element) {
          this.closeForumView(null, tag)
        } else {
          window.location.href = '/1.5/forum/list?tag='+tag;
        }
      }
    },
    onCityTagClicked() {
      if (!this.wId)
        window.location.href = '/1.5/forum/list?src=post&city='+this.post.city + '&prov='+this.post.prov;
    },

    showSMB(){
      RMSrv.share('show');
    },
    // cmntsScrolled(){
    //   var self = this, wait = 400;
    //   self.loading = true;
    //
    //   if (!self.scrollElement) {
    //     this.scrollElement = document.getElementById('recent_cmnts_container');
    //   }
    //   if (!self.waiting) {
    //     self.waiting = true;
    //     setTimeout(function () {
    //       self.waiting = false;
    //       var element = self.scrollElement;
    //       if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
    //         if ((self.post.cmnts.length > self.recent_cmnts.length) && self.dispVar.isApp) {
    //           var index = self.recent_cmnts[self.recent_cmnts.length-1].idx ;
    //           var endIndex = index > self.cmntPageCnt ? (index-self.cmntPageCnt) : 0
    //           for (var i = index; i> endIndex; i--) {
    //             if(self.computedAdmin || !self.post.cmnts[i-1].del)
    //               self.recent_cmnts.push(self.post.cmnts[i-1]);
    //           }
    //           if (self.recent_cmnts.length > self.cmntTotalPageCnt)
    //             self.recent_cmnts.splice(0, self.recent_cmnts.length - self.cmntTotalPageCnt);
    //         }
    //       }
    //       // scroll back
    //       if (element.scrollTop === 0 ) {
    //         var totalLen = self.post.cmnts.length;
    //         var recent_cmnts_first = self.recent_cmnts[0].idx;
    //         if ((totalLen-1 > recent_cmnts_first) && self.dispVar.isApp) {
    //           //push back 5 cmnts.
    //           for (var i = recent_cmnts_first; i<totalLen; i++) {
    //             self.recent_cmnts.unshift(self.post.cmnts[i]);
    //           }
    //           if (self.recent_cmnts.length > self.cmntTotalPageCnt)
    //             self.recent_cmnts.splice(self.recent_cmnts.length - self.cmntTotalPageCnt);
    //         }
    //       }
    //       self.loading = false;
    //     }, wait);
    //   }
    // },
    computeFav: function () {
      if (!this.post.favs) return false;
      return this.post.favs.indexOf(this.dispVar.sessionUser._id) >= 0
    },
    editable() {
      return this.computedAdmin || this.post.uid == this.dispVar.sessionUser._id + '';
    },

    closeForumView(newPostId, tag) {
      if(vars.src=='property') {
        window.location.href="/1.5/forum?city="+this.post.city;
      }
      toggleModal('forumViewModal');
      window.bus.$emit('forum-view-close', {readStatus:this.post.readStatus, gid: this.post.gid, realtorOnly: this.post.realtorOnly, cmntl: this.post.cmntl, _id: this.post._id, vc: this.post.vc, cc: this.post.cc, newPostId: newPostId, tag:tag});
      this.close();
      this.closeView();
    },

    getPostDetail(id, gid, err_cb) {
      var self = this;
      if (self.loading == true) {
        return;
      }
      self.loading = true;
      if (this.dispVar.isApp) {
        var params =  {
          id : id,
          e: 'open',
          type:'forum'
        };
        window.bus.$emit('track-log-event',params);
      }
      self.$http.post('/1.5/forum/detail/' + id,{gid:gid}).then(
        function(ret) {
          if (ret.data.ok) {
            ret.body.post.del = ret.body.post.del || false;
            ret.body.post.discmnt = ret.body.post.discmnt || false;
            self.post = ret.body.post;
            self.authorIsVip = ret.body.authorIsVip;
            self.relatedPosts = ret.body.relatedPosts;
            self.userForm.forumid = self.post._id;
            self.userForm.forumtl = self.post.tl;
            self.initPostDetail();
            self.loading = false;
          } else {
            self.loading = false;
            if (err_cb)
              return err_cb();
            else
              return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          self.loading = false;
          ajaxError(err);
        }
      )
    },
    getRecommend() {
      var self = this;
      var params = {aid:vars.aid};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/recommend',params).then(
        function(ret) {
          if (ret.data.ok) {
            if (ret.data.posts && ret.data.posts.length) {
              self.recommeds = ret.data.posts;
              if (self.recommeds )
              var index = self.recommeds.findIndex(function(post) {
                return post._id == vars.id;
              });
              if (index >=0) {
                self.recommeds.splice(index,1);
              }
            }
            if (ret.data.wecards) {
              self.wecards = ret.data.wecards;
              self.userid = ret.data.userid;
            }
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },

    initPostDetail() {
      var self = this;
      self.post_sticky = self.post.sticky || false;
      self.post_fav = self.computeFav()|| false;
      self.post_pn = self.post.pn;
      self.sticky_cmnts = [];
      self.recent_cmnts = [];
      self.display_cmnts = [];
      self.post.favs = self.post.favs || [];
      if (self.post.edm)
        self.edm = self.formatTs(self.post.edm);
      if (self.post.edmw)
        self.edmw = self.formatTs(self.post.edmw);
      if (self.post.edmd)
        self.edmd = self.formatTs(self.post.edmd);
      self.$nextTick(function () {
        self.formatNews(self.dispVar.isApp);
      });
      self.post.wpHosts = self.post.wpHosts || []
      if (self.post.wpHosts && self.post.wpHosts.length) {
        self.wpmt = self.post.wpHosts[0].mt;
      }
      if (!self.post.cmnts) return;
      for (var i = self.post.cmnts.length; i>0; i--) {
        var element = self.post.cmnts[i-1];
        element.idx = i-1;
      }
      for (var i = self.post.cmnts.length; i>0; i--) {
        var element = self.post.cmnts[i-1];

        if (element.ref>=0) {
          var refcmnt = self.post.cmnts.find(function(cmnt) {
            return cmnt.idx == element.ref;
          });
          if (refcmnt && refcmnt.m) {
            var m = refcmnt.m
            element.refContent  = m.length > 70 ? m.substr(0,100)+'...' : m;
            element.refUser  = refcmnt.fornm;
            element.refTs  = this.formatTs(refcmnt.ts);
            if (refcmnt.origM) {
              element.refOrigContent  = refcmnt.origM.length > 70 ? refcmnt.origM.substr(0,100)+'...' : refcmnt.origM;
            }
          }
        }
        // if (element.sticky)
        //   self.sticky_cmnts.push(element);

        // only add deleted comments for admin.
        if(self.computedAdmin|| !element.del)
          self.recent_cmnts.push(element);
      }
      self.recent_cmnts.sort(self.sortCmnt);
      self.display_cmnts = self.recent_cmnts.slice(0,19);
    },
    sticky(sticky) {
      var self = this;
      var params = {type:'sticky', sticky:sticky, city: self.post.city, prov: self.post.prov};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/sticky/' + self.post._id,params ).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.sticky = sticky;
            self.post_sticky = sticky;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },

    favourite(fav) {
      var self = this;
      var params = {fav:fav};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/fav/' + self.post._id, params).then(
        function(ret) {
          if (!ret.data.ok)
            return window.bus.$emit('flash-message', ret.data.e);
          self.post_fav = fav
          if (fav) {
            self.post.favs.push(self.dispVar.sessionUser._id)
          } else {
            self.post.favs.splice(self.post.favs.indexOf(self.dispVar.sessionUser._id),1)
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },

    showComments(index) {
      if (this.wId || (!this.dispVar.isApp && !this.dispVar.canWebComment))
        return window.location.href = '/app-download';
      if(!this.dispVar.isLoggedIn)
        return window.location.href = '/1.5/user/login';
      this.showCommentDialog = true;
      // if (!/iPhone|iPad|iPod|huawei/i.test(navigator.userAgent.toLowerCase())) {
      //   this.cmntsBottomHeight = window.innerHeight/2;
      //   this.cmntsPublishBottomHeight = this.cmntsBottomHeight - 10;
      // }
      // this.showMask = true;
      this.cmntsMarginTop = 20;
      this.cmntsHeight = 180;
      this.cmntsWidth = 100;
      this.cmntsBorderRadius = 0;
      this.cmntsBarFooterHeight = 250;
      this.cmntFontSize = 15
      this.showMask = true;
      if (index>=0) {
        this.referIdx = index;
      }
    },

    manageComment(params, cb) {
      var self = this;
      if(this.post.gid) {
        params.gid = this.post.gid;
      }
      self.$http.post('/1.5/forum/cmnts/' + self.post._id, params).then(
        function(ret) {
          if (ret.data.ok)
            return cb(ret.data);
          else
            return window.bus.$emit('flash-message', ret.data.e);
        },
        function(err) {
          ajaxError(err);
        }
      );
    },

    deleteComment(des, index, action, wpHosts) {
      var self = this, delFlag;
      var params = {action:'del', index: index, wpHosts: wpHosts, city: self.post.city, prov: self.post.prov};
      if (action == 'delete') {
        params.del = des;
        delFlag = true;
      } else if (action == 'recover') {
        params.del = false;
        delFlag = false;
      }
      var des = des;
      self.manageComment(params, function(ret) {
        self.post.cmnts[index].del = des || false;
        self.post.cmntl = ret.cmntl;
        // self.initPostDetail();
        var recent_cmnt_index = self.recent_cmnts.findIndex(function(el) {return el.idx === index;});
        if (recent_cmnt_index>=0) self.recent_cmnts[recent_cmnt_index].del = des || false;
        // var sticky_cmnt = self.sticky_cmnts.find(function(el) { return (el.idx === index)});
        // if (sticky_cmnt) sticky_cmnt.del = des || false;
      });
    },
    deleteCmntConfirm(action, index, wpHosts) {
      event.stopPropagation();
      this.deleteMethod = this.deleteComment;
      var msg = this.$parent._(action + ' cmnt #') + (index + 1);
      var placeholder = this.$parent._('Enter delete reason');
      //add parentName for a workaround to not active the dialog in forumList.
      return window.bus.$emit('show-confirm-dialog', {id: index, wpHosts: wpHosts, parentName:'forumView', action: action, msg:msg, placeholder: placeholder});
    },

    stickyComment(index, sticky) {
      var self = this;
      var params = {action:'sticky', index: index, sticky: sticky, city: self.post.city, prov: self.post.prov};
      self.manageComment(params, function(ret) {
        self.post.cmnts[index].sticky = sticky;
        self.post.cmntl = ret.cmntl;
        var recent_cmnt_index = self.recent_cmnts.findIndex(function(el) {return el.idx === index;});
        if (recent_cmnt_index>=0) {
          self.recent_cmnts[recent_cmnt_index].sticky = sticky;
        }
      });
    },
    savePost(cb) {
      var self = this;
      self.$http.get('/1.5/forum/publish/'+this.post._id +'?src=property&tl='+this.post.tl+'&city='+this.post.city+'&prov='+this.post.prov+'&img='+vars.img).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            cb();
          } else {
            return window.bus.$emit('flash-message', ret.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    saveNickName(nickname) {
      var self = this;
      self.dispVar.sessionUser.fornm = nickname;
      this.showNickNameMask = false;
      self.publishCmntConfirm();
    },
    cancelNickName() {
      this.showNickNameMask = false;
    },
    publishCmnt() {
      //set forum nickname
      var self = this;
      if (!self.comment || !self.comment.trim())
        return window.bus.$emit('flash-message', self.$parent._('Content needed'));
      if(self.dispVar.sessionUser.fornm) {
        self.publishCmntConfirm();
      } else {
        this.showNickNameMask = true;
        this.closeCommentsDialog();
        return window.bus.$emit('show-edit-nickname');
      }
    },
    addCmnt() {
      var self = this;
      if (!self.post.cmnts)
        self.post.cmnts=[];
      var cmnt_len = self.post.cmnts.length;
      if (cmnt_len > 0 && self.post.cmnts[cmnt_len-1].uid == self.dispVar.sessionUser._id && self.post.cmnts[cmnt_len-1].m == self.comment )
        return window.bus.$emit('flash-message', self.$parent._('Duplicated comments not allowed', 'forum'));
      var params = {action:'add', comment:self.comment, src: self.post.src};
      if (self.referIdx >= 0)
        params.ref = self.referIdx
      self.manageComment(params, function(ret) {
        self.post.cmnts = ret.cmnts;
        self.post.cmntl = ret.cmntl;
        self.initPostDetail();
        self.publishing = false;
        document.getElementById('recent_cmnts_container').scrollIntoView();
        self.close();
      });
    },
    publishCmntConfirm() {
      var self = this;
      self.publishing = true;
      if (self.post.status === 'new') {
        this.savePost(self.addCmnt);
      } else {
        self.addCmnt();
      }
    },
    closeView() {
      this.relatedPosts = [];
      this.recent_cmnts = [];
      this.display_cmnts = [];
      this.sticky_cmnts = [];
      this.showReaders = false;
      this.loading = false;
      this.post = {
        del:null,
        tl:'',
        m:'',
        sticky:false,
        cmnts:[],
        favs:[],
        city:null,
        prov:null,
        avt:null,
        photos:[]
      };
    },
    closeCommentsDialog() {
      this.showCommentDialog = false;
      this.showAuthorDialog = false;
      this.showMask = false;
      this.cmntsBottomHeight = 0;
      this.cmntsPublishBottomHeight = 0;
      this.cmntsMarginTop = 10;
      this.cmntsWidth=40;
      this.cmntsHeight=30;
      this.cmntsBorderRadius = 50;
      this.cmntsBarFooterHeight = 50;
      this.cmntFontSize = 10;
    },
    close() {
      this.closeCommentsDialog();
      this.comment = '';
      this.referIdx = undefined;
      this.edm = null;
      this.edmd = null;
      this.edmw = null;
      if (this.computedAdmin)
        this.cmntsWidth = '35';
    },
    deletePostConfirm(action) {
      event.stopPropagation();
      this.showMask = true;
      this.deleteMethod = this.deletePost;
      var title = this.post.tl? this.post.tl.substring(0,10): '';
      var msg = this.$parent._(action + ' post?');
      if (action=='db_delete')
        msg= this.$parent._('delete post from db?');
      var placeholder = this.$parent._('Enter delete reason');
      return window.bus.$emit('show-confirm-dialog', {id: this.post._id, action: action, msg:msg, placeholder: placeholder});
    },
    deletePost(des, id, action) {
      var self = this;
      if (!id) return;
      var params={id:id, city: self.post.city, prov: self.post.prov}, delFlag;
      if (action == 'delete') {
        params.del = 'delete';
        params.del_des = des;
        delFlag = true;
      } else if (action == 'recover') {
        params.del = 'recover';
        delFlag = false;
      } else if (action == 'db_delete') {
        params.del = 'db_delete';
        delFlag = true;
      }
      if(this.post.gid) {
        params.gid = this.post.gid;
      }
      if(this.post.wpHosts) {
        params.wpHosts = this.post.wpHosts;
      }
      self.$http.post('/1.5/forum/edit', params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.post.del = delFlag;
            if (params.del == 'db_delete') {
              self.closeForumView();
            }
          } else {
            return window.bus.$emit('flash-message', ret.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
  },
  mounted () {
    // if link from property, open exist or create a new one.
    var self = this;
    window.bus.$on('wSignChange', function (d) {
      self.wSign = d;
    });
    window.bus.$on('wCommentChange', function (d) {
      self.wComment = d;
    });
    if (vars.nobar) {
      document.querySelector('#postViewContainer').style.top = '0px'
    }
    if (vars.src === 'property' && vars.id) {
      self.post._id = vars.id;
      self.getPageData(this.datas, {}, true);
      self.getPostDetail(vars.id, null, function() {
        self.post.src = 'property';
        self.post.status = 'new';
        if (vars.tl) {
          // self.post.tl = 'ID: ' + vars.sid + ' ' + vars.tl;
          self.post.tl = vars.tl;
          self.post.m = 'ID: ' + vars.sid;
        }
        if (vars.img) {
          self.post.photos.push(vars.img);
        }
        if (vars.city) {
          self.post.city = vars.city;
        }
        if (vars.prov) {
          self.post.prov = vars.prov;
        }
      });
      document.getElementById('forumViewModal').classList.remove('modal');
    } else if (vars.id) {
        if (vars.share) {
          this.wId = true;
          if (vars.wct == 0) {
            this.wct = false;
          } else {
            this.wct = true;
          }
          this.wDl = true;
        }
        this.id = vars.id;
        this.datasObj = {};
        if (vars.aid) {
          this.datas.push('ownerData');
          this.datasObj.ownerId = vars.aid;
          this.aId = true;
        }
        this.getPageData(this.datas, this.datasObj, true);
        document.getElementById('forumViewModal').classList.remove('modal');
        if (!this.nobar)
          document.querySelector('.icon-close').classList.add('hide');
        this.wDl = true;
        this.getPostDetail(this.id, vars.gid);
        this.getRecommend();
    }
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.userForm.nm = self.dispVar.sessionUser.nm || null;
      self.userForm.eml = self.dispVar.sessionUser.eml || null;
      self.userForm.mbl = self.dispVar.sessionUser.mbl || null;
    });
    bus.$on('open-forum-view', function (params) {
      var id = params.id;
      self.section = params.section;
      if(params.city)
        self.city = params.city;
      self.displayPage = params.displayPage;
      self.picUrls = [];
      self.getPostDetail(id, params.gid);
      self.picUrls = []
      toggleModal('forumViewModal');
      //scroll to top
      document.querySelector('#postViewContainer').scrollTop = 0
      setTimeout(function() {
        if(!params.gid) {
          self.$http.post('/1.5/forum/updateForumhist', {id: id}).then(
            function (ret) {},
            function(err) {
              ajaxError(err);
            }
          );
        }
      }, 5000);
      if (this.computedAdmin)
        this.cmntsWidth = '35'
    });
    bus.$on('close-confirm-dialog', function(d) {
      self.showMask = false;
    });
  },
  components: {
    FlashMessage,
    ConfirmWithMessage,
    ForumCmntCard,
    ShareDialog,
    ForumShareDesc,
    GetAppBar,
    CreateNickname,
    UserBriefInfo,
    SignUpForm,
    AppFormPreviewModal
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.disabled{
  background: #a9a3a3 !important;
}
.readers .bar-footer .btn {
  border: none;
}
.readers {
  position: fixed;
  top: 44px;
  padding: 15px;
  z-index: 200;
  background-color: white;
  color: black;
  font-size: 15px;
  width: 100%;
  height:100%;
}
.reader-wrapper{
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding: 5px 10px;
}
.reader-wrapper span{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.reader-wrapper .nm{
  width: 45%
}
.reader-wrapper .status{
  width: 15%;
}
.reader-wrapper .time{
  width: 40%
}
.read-status {
  color: white;
  position: relative;
  z-index: 20;
  display: flex;
  align-items: center;
}
.realtor-only {
  background: #e03131;
  color: #f1f1f1;
  border-radius: 3px;
  padding: 1px 4px 2px;
  font-size: 11px;
}
.pull-spinner {
  top: 200px;
  position: relative;
}
.signupform {
  border: 1px solid #d2cbcb;
  padding: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.post-top-container {
  margin-bottom: 20px;
  position: relative;
}
.post-top-container img {
  width: 100%;
  margin-bottom: -5px;
}

.mainTopic {
  color:#E03131
}
.authorInfo {
  position: fixed;
  top: 100px;
  padding: 15px;
  z-index: 200;
  background-color: white;
  color: black;
  font-size: 15px;
  width: 90%;
  left: 5%;
}
.nickname-mask {
  z-index: 30px;
}
.download-button i {
    font-size: 22px;
    vertical-align: middle;
}
.download-button {
  margin: 30px 0;
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  color: #fff!important;
  text-decoration: none;
  border-radius: 4px;
  background-color: #42c02e;
  height: 40px;
  display: block;
}
.tp-title {
  background-color: #E03131;
  padding: 10px;
  color: #fff;
}
.topic-content img {
  width: 50px;
  height: 50px;
}
.topic-content .content-right{
  width: calc(100% - 70px);
  float:right;
  height: 45px;
  padding-top:5px;
}
.topic-content {
  height: 70px;
  padding: 10px;
  border-bottom: 1px solid #dedada;
  width: 100%;
  background-color: #f2f2f2;
}
.post-topics{
  margin-top: 20px;
  margin-bottom: 20px;
}
.comments-header {
  border-bottom: 1px solid #ececec;
  display: table;
  width: 100%;
  color:#878787;
  height: 40px;
  padding-top: 20px;
}
.comments-header fa {
  line-height: inherit;
}
.bar-standard .btn{
  background-color: #E03131;
  color: white;
  font-size: 17px;
}
[v-cloak] {
  display: none;
}
.post-title {
  padding-bottom: 20px;
  font-size: 22px !important;
  line-height: 1.3;
}
.post-title a {
  color: black!important;
}
pre {
  white-space: pre-wrap;
}
#postViewContainer {
  top:40px;
  width: 100%;
  margin-bottom: 50px;
  overflow: auto;
  padding: 20px 10px 70px 10px;
}
.post-author {
  width: 100%;
  height: 50px;
}
.post-img {
  width: 30px;
  float: left;
  border-radius: 30px;
  margin-right: 20px;
  height: 30px;
  background-size: 100% 100%;
  background-color: #eaebec;
}
.name {
  font-size: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  float: left;
  padding-right: 5px;
}
.ts {
  font-size: 10px;
  float: left;
  width:100%;
  color:#a9a3a3;
}
.bar-footer .fa-heart-o,.bar-footer .fa-heart, .bar-footer .icon-edit,.bar-footer .icon-trash {
  font-size: 22px!important;
}
.bar-footer {
  height: 50px;
  background-color: #efefef;
  border: none;
  z-index: 20!important;
}
.bar-footer .nickname {
  max-width: 120px;
  overflow: hidden;
  font-size:12px;
  color:grey;
  padding-left:5px;
}
.bar-footer .icon {
  color: #e03131;
  top: 3px;
  width: 24px;
  height: 24px;
  padding-top: 0;
  padding-bottom: 0;
  display: block;
  margin: auto;
  font-size: 21px;
}
.bar-footer .btn {
  margin-left: 5%;
  margin-top: 4px;
  border-radius: 0px;
}
.bar-footer .fa-rmshare {
  font-size: 19px;
  padding-top: 1px;
}
.post-tags {
  padding-bottom: 10px;
  padding-top: 10px;
}
.post-tags .btn {
  margin-top:10px;
  margin-right: 10px;
  border-radius: 20px;
  min-width: 120px;
}
#forumViewModal{
  z-index: 14;
  height: 100%;
  overflow: hidden;
}
#forumViewModal.active{
  transform: translate3d(0,0,0px);
}
.comment-dialog textarea {
  height: 100%;
  padding: 12px;
  border-radius: 0px;
}
.comment-dialog {
  position: fixed;
  top: 50%;
  width: 100%;
  z-index: 20;
  height: 100%;
}
.post-comments {
  width: 100%;
}
.post-comments .icon {
  float: right;
  padding:10px;
}
.sticky-comments{
  background-color: #fdfafb;
  margin-top:20px;
  margin-bottom: 20px;
}
.cross {
  text-decoration: line-through;
}
.deleted {
  background-color: #E03131;
  text-decoration: line-through;
}
.comment-input-div {
  padding: 7px;
  width: 50%;
  margin-top: 8px;
  border-radius: 60px;
  float: left;
  border: 1px solid;
  border-color: #a29a9a;
  font-size: 14px;
  height: 35px;
  background-color: white;
}
#recent_cmnts_container{
  min-height: 20px;
  overflow-y: hidden;
  padding-bottom: 10px;
}
#shareDialog #share-content{
  bottom: 0px !important;
}
.hide {
  display: none;
}
.post-photo{
  padding-top:20px;
}
.post-photo img {
  max-width: 100%;
  max-height: 500px;
}
.img-wrapper {
}
.mask {
  background: rgba(0,0,0,0.8);
  opacity: 0.7;
  z-index: 18;
}
.comment-dialog-footer {
  z-index: 20;
}
#commentTextarea {
  float: left;
  padding-top:5px;
  font-size: 10px;
  resize: none;
}

.wDlBottom {
  bottom: 50px;
}
.post-tags-admin .btn {
  background-color: #E03131;
  color: white;
}
.post-content {
  position: relative;
  background-color: #fff;
  word-wrap: break-word;
  font-size: 17px !important;
  line-height: 1.5;
  width: 100%;
  overflow: auto;
}
.post-content img {
  max-width: 100% !important;
}
.post-content iframe {
  max-width: 100% !important;
}
.comment-dialog-footer span {
  padding: 15px 10px 20px 10px;
}
.fa-long-arrow-down, .fa-long-arrow-up {
  padding-top: 2px;
}

.img-wrapper .search {
  background-color: rgba(0,0,0,0.2);
  position: absolute;
  bottom: 10px;
  right:10px;
  padding: 5px 20px;
  border-radius: 20px;
}
.img-wrapper .search .details {
  display: inline;
  float: left;
  padding-right: 10px;
  padding-top: 2px;
  color: white;
}
.img-wrapper .search .fa {
  display: inline;
  color: #fff;
  font-size:24px;
}
.bar-footer .tab-label {
  display: block;
  font-size: 10px;
  color: #666;
}
.bar-footer a {
  text-align: center;
  vertical-align: middle;
  height: 50px;
  cursor: pointer;
  float: right;
  padding: 5px 7px 0 7px;
}
.comment-number {
  font-size: 9px;
  position: absolute;
  border: none;
  padding: 1px 1px;
  width: 15px;
  margin-left: -6px;
  border-radius: 5px;
  color: white !important;
  background: #e03131;
}
</style>
