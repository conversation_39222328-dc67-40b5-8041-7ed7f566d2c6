ONE_DAY = 86400000; //1000 * 3600 * 24
var CHECK_INVIEWPORT_GAP = 82;
var discover = {
  data() {
    return {
      fixDate:false,
      fixTabs:false,
      page: 0,
      waiting: false,
      hasMoreProps: true,
      loadMoreEdmProps:true,
      loading: false,
      scrollLoading: false,
      propTag:{
        k: "ts", v: "new"
      },
      lastPropTag:{
        k: "ts", v: "new"
      },
      propTags:[],
      selectedCmty:{},
      selectedCmtyIdx:null,
      cmtys:[],
      cmtyProps:[],
      cmtyPropsObj:{},
      sortedDates:[],
      savedSearches:{},
      userCity:{},
      edmLogs:[],
      curFixedEdmIndex:0,
      curDisPlayEdmDate:'',
      curDate:null,
      curCollEdmDate:null,
      feedDates:[],
      lastScrollTop:'',
      scrollDirection:'',
      initMiddlePartheight:false,
      showPriceChange:true,
      dispVar:{
        lang:'en',
        edmDetail:{
          hasNewChange: false,
          mlsMt: ""
        },
      },
      // curDisPlayEdmGroup:'',
      // curDisPlayEdmGroupName:'',
      curFixedEdmGroupIndex:0,
      feedTags:['All','Homes','Searches','Communities','Locations'],
      activeFeedTag:'All',
      feedTagChanged:false,
      currentEdmLogProp:{},
      showMask:false,
      watchingType:'cmty',
      // showing部分
      agentList:[],
      upcomingList:[],
      completedList:[],
      completedAllList:[],
      buylist:[],
      showingTag:'upcoming',
      showingTags:['upcoming','Completed','Buylist'],
      timer:null,
      reloadTime:0,
      hasNewFeed:false,
      updateViewStatus:false,
      // showCmtyCard:false,
      propList:[],
      buylistMore:false,
      completedMore:false,
    }
  },
  computed:{
    showCmtyCard(){
      if(this.propTag.k == 'feed'){
        return false;
      }
      if(this.selectedCmty._id && (this.selectedCmty.avgP || this.selectedCmty.mom || this.selectedCmty.yoy)){
        return true;
      }else{
        return false;
      }
    },
  },
  mounted() {
    this.$getTranslate(this);
    var self = this;
    bus.$on('pagedata-retrieved', function (d) {
      // 在indexHeader中发送datas请求 'userCity','savedSearches','favCmtys','propTags','lang'
      self.dispVar = Object.assign(self.dispVar, d);
      self.userCity = d.userCity;
      self.savedSearches = d.savedSearches;
      self.propTags = Object.assign([{k:'feed',desc:'feeds'}], d.propTags);
      self.cmtys = d.favCmtys;
      var defaultHomeTab = '';
      if(d.sessionUser && d.sessionUser.defaultHomeTab){
        defaultHomeTab = d.sessionUser.defaultHomeTab
      }else if(localStorage.defaultHomeTab){
        defaultHomeTab = localStorage.defaultHomeTab;
      }
      if(defaultHomeTab =='feed'){
        self.propTag = self.lastPropTag = {k:defaultHomeTab};
        self.getEdmProps();
      }else{
        self.getPropsForTag();
      }
      if(d.isRealtor){
        self.feedTags.push('Showings')
      }
      if(d.edmDetail.hasNewChange){
        self.hasNewFeed = true;
        self.alertNewFeed()
      }
      if(d.isLoggedIn){
        self.getAgentList();
      }
    });
    self.scrollElement = document.getElementById('content');
    self.scrollElement.addEventListener('scroll', function() {
      self.handleLoadMore();
    });
    bus.$on('popup-match-list', function (d) {
      self.currentEdmLogProp = d;
      self.showMask = true;
      self.toggleModal('MatchListPopup','open')
    });
    bus.$on('close-watch-popup', function () {
      self.closePopup()
    });
    bus.$on('watch-prop', function ({unwatch,prop}) {
      var idx = self.cmtys.findIndex(e=> e._id == prop._id)
      if(unwatch){
        self.cmtys.splice(idx, 1);
        if(self.cmtys.length){
          var index = idx-1 < 0 ? idx-1 : 0
          self.selectCmty(self.cmtys[index],index)
        }else{
          self.selectCmty({},0,true)
        }
      }else{
        self.cmtys.splice(idx, 1,prop);
      }
    });
    if(this.timer){
      self.selectTag(self.propTag,1);
      self.clearTimer();
    };
    self.reloadTimer();

    this.appStateCallback = (opt)=>{
      if (opt && opt.nextAppState == 'active') {
        RMSrv.action('pushToken');
        self.reloadTime = new Date().getTime();
        self.selectTag(self.propTag,1);
        self.getEdmLastUpdataTs()
        if(RMSrv.setCookie){
          RMSrv.setCookie();
        }
        self.reloadTimer();
      }else{
        self.clearTimer();
      }
    }
    if(RMSrv.onAppStateChange){
      RMSrv.onAppStateChange(this.appStateCallback)
    }
  },
  watch:{
    fixTabs(new_val){
      if (new_val && (this.propTag.k != 'feed')) {
        this.scrollBar();
      }
    },
  },
  methods: {
    // showing部分
    openDetail:function(prop) {
      var id = (/^RM/.test(prop.id))?prop.id:prop._id;
      openPopup(`/1.5/prop/detail/inapp?id=${id}`,this.$_('RealMaster'));//lang=${this.lang}
    },
    showAgentWesite(user){
      var url = `/1.5/wesite/${user._id}?inFrame=1`;
      RMSrv.openTBrowser(url);
    },
    selectShowingTag(tag) {
      this.showingTag = tag;
      document.querySelector("#home-showing").scrollIntoView(true);
      if(tag == 'Buylist'){
        calcMktWraper();
      }
    },
    getAgentList(){
      var self = this;
      fetchData('/1.5/crm/linkedAgents',{body:{}},function(err,ret) {
        self.loading = false;
        if (ret.ok == 1 && ret.list.length > 0) {
          self.agentList = ret.list;
          self.getShowingList();
          self.getBuyList();
        }
      });
    },
    crmOpenShowing(showing){
      if(! showing._id){
        return ;
      }
      var url = '/1.5/showing/detail/share?d=/1.5/index&showingId='+showing._id +'&isPopup=1&lang='+this.dispVar.lang;
      openPopup(url,'',true);
    },
    getDay(date) {
      var d = new Date(date.replace(/-/g, "/").split(' ')[0]);
      var weekday = [
         "Sunday","Monday","Tuesday", "Wednesday","Thursday","Friday","Saturday"
      ];
      return weekday[d.getDay()];
    },
    formatHM: function (ts) {
      if (ts) {
        return  (ts.getHours()<10?'0'+ts.getHours():ts.getHours()) + ":" + (ts.getMinutes()<10?'0'+ts.getMinutes():ts.getMinutes())
      } else
        return '';
    },
    date2Num: function (ts) {
      if (ts) {
        return ''+ts.getFullYear()+ ((ts.getMonth()+1)<10?'0'+(ts.getMonth()+1):(ts.getMonth()+1))  + (ts.getDate()<10?'0'+ts.getDate():ts.getDate())
      } else
        return '';
    },
    getShowingList(){
      var afterDate = new Date(),time;
      time = afterDate.setDate( afterDate.getDate()- 180 );
      afterDate = new Date(time)
      var b = {
        today: this.date2Num(new Date()),
        now: this.formatHM(new Date()),
      };
      var self = this;
      self.loading = true;
      fetchData('/1.5/showing/client',{body:b},function(err,ret) {
        self.loading = false;
        if (ret && ret.ok == 1 && ret.list && ret.list.list) {
          self.upcomingList = ret.list.list
          self.upcomingList = self.formatShowingData(self.upcomingList)
          if(ret.list.past){
            self.completedList = self.formatShowingData(ret.list.past);
            if(self.completedList.length > 5){
              self.completedAllList = self.completedList;
              self.completedList = self.completedList.slice(0,5);
              self.completedMore = true;
            }
          }
        }
      });
    },
    formatShowingData(list){
      var self = this;
      list.forEach(ele=>{
        ele.scheduled=0
        ele.day = self.getDay(ele.dt)
        ele.props.forEach(element=>{
          if(element.stT){
            ele.scheduled++
          }
        })
      })
      return list
    },
    getBuyList(){
      var b = {
        limit:6
      };
      var self = this;
      self.loading = true;
      fetchData('/1.5/showing/buylist/client',{body:b},function(err,ret) {
        self.loading = false;
        if (ret && ret.ok == 1 && ret.list) {
          ret.list = ret.list || [];
          ret.list.forEach(ele=>{
            ele.realtor = ele.realtor || {};
          })
          self.buylist = ret.list
          self.buylistMore = ret.more
        }
      });
    },
    goToBuyList(l){
      var url = '/1.5/showing/buylist/client?d=/home/<USER>'
      if(l && l.showing && l.showing._id){
        url += '&id='+l.showing._id
      }
      gotoLink(url,{'key':'buylist','val':'discover'});
    },
    showAllCompletedList(){
      this.completedList = this.completedAllList;
      this.completedMore = false;
    },
    // discover部分
    alertNewFeed(){
      var self = this;
      setTimeout(() => {
        if(self.hasNewFeed){
          self.hasNewFeed = false;
          self.updateViewStatus = true;
        };
      }, 30 * 1000);
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    reloadTimer() {
    // 每15分钟更新数据
      var self = this;
      self.clearTimer();
      this.timer = setInterval(() => {
        var time = new Date().getTime()
        if(time - self.reloadTime < 15 * 60 * 1000){
          return
        }
        self.reloadTime = time;
        self.selectTag(self.propTag,1);
        self.getEdmLastUpdataTs()
        if(RMSrv.setCookie){
          RMSrv.setCookie();
        }
      }, 15 * 60 * 1000);
    },
    clickMask() {
      this.showMask = false;
      this.toggleModal('MatchListPopup','close');
    },
    toggleModal(a,b){
      toggleModal(a,b)
    },
    selectFeedTag(tag,reload){
      if(!reload){
        this.activeFeedTag = tag;
        trackEventOnGoogle('homeDiscover','feed',tag);
        document.querySelector("#discover-container").scrollIntoView(true);
      }
      this.handleFeedTagClick();
      this.getEdmProps();
    },
    handleFeedTagClick:function() {
      this.feedTagChanged = true;
      this.curCollEdmDate = null;
      this.loadMoreEdmProps = true;
      this.feedDates=[];
      this.fixDate = false;
      this.fixTabs = false;
      this.edmLogs = [];
    },
    editSubscribeCmty() {
      this.showMask = true;
      bus.$emit('watch-item-change', {item:this.selectedCmty});
      // bus.$emit('checkNotify',{});
      toggleModal('watchingModal','open')
    },
    closePopup(){
      this.showMask=false;
      toggleModal('watchingModal','close')
    },
    isInViewport(elementId) {
      var element = document.getElementById(elementId);
      if (!element) {
        return false;
      }
      var {top,left,width,height} = element.getBoundingClientRect();
      return (
        top < (window.pageYOffset + window.innerHeight) &&
        left < (window.pageXOffset + window.innerWidth) &&
        (top + height-80) > window.pageYOffset &&
        (left + width) > window.pageXOffset
      );
    },
    isGroupInViewport(elementId) {
      var element = document.getElementById(elementId);
      if (!element) {
        return false;
      }
      var {top,height} = element.getBoundingClientRect();
      var topDistence = (this.curFixedEdmIndex > 0 && this.scrollDirection > 0)? 48+44+41 : 48+44
      return (top < topDistence) && ((top + height-80) > window.pageYOffset);
    },
    getScrollDirection() {
      var discoverWrapper = document.getElementById('content');
      var st = discoverWrapper.pageYOffset || discoverWrapper.scrollTop; // Credits: "https://github.com/qeremy/so/blob/master/so.dom.js#L426"
      if (st > this.lastScrollTop){
        //downscroll code
        this.scrollDirection = 1;
      } else {
        // upscroll code
        this.scrollDirection = 0;
      }
      this.lastScrollTop = st;
      this.fixedDate();
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    checkEdmPropsExist:(props)=>{
      return (props && Object.keys(props).length > 0);
    },
    showSeeAllBtn:(props)=>{
      return (props && Object.keys(props).length > 5);
    },
    calcGroupName(group){
      if(group.chgd){
        return this.$_('Updates of saved and watched')
      }else if(group.new || group.off){
        if(group.isFavCmty){
          return this.$_('Saved community updates')
        }else{
          return this.$_('Saved search updates')
        }
      }else{
        return '';
      }
    },
    fixedDate() {
      var propsArray = this.cmtyProps,headerHeight = 44;
      var fixDateTop = headerHeight + 49;// header+fixedTab高度
      if (this.propTag.k == 'feed') {
        propsArray = this.edmLogs;
        fixDateTop = headerHeight + 47 + 59;// header+fixedTab+saves分类tab高度
      }
      if (propsArray.length) {
        var cmtyBarBoundData = document.querySelector('#scrollToFixed').getBoundingClientRect();
        var propsBoundData = document.querySelector('.propsContainer').getBoundingClientRect();
        this.fixTabs = (cmtyBarBoundData.top <= headerHeight);
        this.fixDate = (propsBoundData.top <= fixDateTop); // 日期固定位置的高度()
        if (this.fixDate) {
          var curDisPlayEdmlogs = propsArray[this.curFixedEdmIndex]
          var curDisPlayEdmDate = curDisPlayEdmlogs.id;
          if (this.scrollDirection) {
            // data
            if (this.isInViewport(curDisPlayEdmDate)) {
              this.curDisPlayEdmDate = curDisPlayEdmDate;
            } else {
              this.curFixedEdmIndex += 1;
              this.curFixedEdmGroupIndex=0;
              return ;
            }
            if(this.propTag.k == 'feed'){
              // this.fixEdmLogFolder(curDisPlayEdmlogs.edm[this.curFixedEdmGroupIndex])
            }
          } else {
            // scroll up
            if (this.curFixedEdmIndex > 0) {
              var lastEdmlogs = propsArray[this.curFixedEdmIndex-1]
              var lastEdmDate = lastEdmlogs.id;
              if (this.isInViewport(lastEdmDate)) {
                this.curDisPlayEdmDate = lastEdmDate;
                this.curFixedEdmIndex -= 1;
                if (this.propTag.k == 'feed' && lastEdmlogs.edm) {
                  this.curFixedEdmGroupIndex=lastEdmlogs.edm.length-1;
                }
                return
              }
            }
            if(this.propTag.k == 'feed'){
              var lastEdmlogs = propsArray[this.curFixedEdmIndex]
              // this.fixEdmLogFolder(lastEdmlogs.edm[this.curFixedEdmGroupIndex])
            }
          }
        }
      }
    },
    formatUTC2Locale(time){
      if(!time){
        return ''
      }
      var locale = 'en-CA';
      var opt = { year: 'numeric', month: '2-digit', day: '2-digit' };
      var localeDate = new Date(time).toLocaleDateString(locale,opt)
      // var dateArr = localeDate.split('/');
      // for (let index = 0; index < 3; index++) {
      //   if(dateArr[index].length<2){
      //     dateArr[index] = '0'+dateArr[index]
      //   }
      // }
      // return dateArr.join('-');
      return localeDate
    },
    getEdmLastUpdataTs: function() {
      var self = this;
      fetchData('/1.5/community/edmupdatets',{body:{}},function(err,ret) {
        if (ret.ok == 1 && ret.edmLog) {
          self.dispVar.edmDetail = Object.assign(self.dispVar.edmDetail, ret.edmLog);
          if(ret.edmLog.hasNewChange){
            self.hasNewFeed = true;
            self.alertNewFeed();
          }
        }
      });
    },
    getEdmProps: function() {
      if (!this.loadMoreEdmProps) {
        return;
      }
      if (this.loading) {
        return;
      }
      this.loading = true;
      var date = this.curCollEdmDate;
      var dates;
      if (date) {
        // date = new Date(new Date(date).getTime() - ONE_DAY)
        this.feedDates.push(date);
        dates = this.feedDates;
      }
      var body = {date:dates,tag:this.activeFeedTag,updateViewStatus:this.updateViewStatus};
      var self = this;
      fetchData('/1.5/community/edmProps',{body},function(err,ret) {
        self.loading = false;
        self.updateViewStatus = false;
        if (ret.ok == 1 && ret.edmLog) {
          self.processEdmLog(ret.edmLog);
          if(self.feedTagChanged){
            self.edmLogs = [];
            self.feedTagChanged = false;
          }
          self.edmLogs = self.edmLogs.concat(ret.edmLog);
          self.curDate = ret.edmLog.id;
          self.curCollEdmDate = ret.edmLog.edmId;
        } else {
          self.loadMoreEdmProps = false;
        }
        setTimeout(() => {
          var curDateContainer = document.getElementById(self.curDate);
          if (curDateContainer) {
            //if edm props not able to scroll, load the next edm props
            var container = document.getElementById('discover-container').getBoundingClientRect();
            var edmBlock = curDateContainer.getBoundingClientRect();
            if (container.bottom > edmBlock.bottom) {
              self.getEdmProps();
            }
          }
        }, 10);
      });
    },
    processEdmLog:function(edmLog) {
      for (var i = 0; i < edmLog.edm.length; i++) {
        var edmFeed = edmLog.edm[i];
        if (edmFeed.new) {
          edmFeed.showAllSale = false;
        }
        if (edmFeed.off) {
          edmFeed.showAllSold = false;
        }
      }
    },
    buildPropListLink:function({search,sold,cmty}) {
      var mode = 'list';
      var searchQuery = '';
      for (var [key, value] of Object.entries(search)) {
        if (value) {
          if (sold && (key == 'dom') && (value >= 0)) {
            value = -90;
          }
          if (key == 'bbox') {
            mode = 'map';
          }
          searchQuery += `&${key}=${value}`;
        }
      }
      if (sold) {
        searchQuery += '&soldOnly=true&dom=-90';
      }
      if (cmty) {
        searchQuery += `&cmty=${cmty}`;
      }
      return `/1.5/mapSearch?mode=${mode}${searchQuery}`;
    },
    selectCmty: function(cmty,cmtyIdx,city) {
      document.querySelector("#discover-container").scrollIntoView(true);
      if (this.selectedCmty._id == cmty._id) {
        return;
      }
      trackEventOnGoogle('homeDiscover','cmty',cmty._id)
      this.handleCmtyTagClick();
      // BUG: if use manual compute. should compute after set
      // this.computedShowCmtyCard();
      if(city){
        this.selectedCmty = {};
        this.selectedCmtyIdx = null;
      }else{
        this.selectedCmty = cmty;
        this.selectedCmtyIdx = cmtyIdx;
        this.getCmtyDetail();
      }
      this.getPropsForTag();
    },
    selectTag(tag,reload) {
      if (this.propTag.k != tag.k && !reload) {
        this.propTag = tag;
      }
      // this.computedShowCmtyCard();
      if(!reload || this.fixTabs){
        document.querySelector("#discover-container").scrollIntoView(true);
      }
      this.scrollBar();
      this.handleCmtyTagClick();
      if (tag.k == "feed"){
        if(tag.from == 'alert'){
          this.updateViewStatus = true;
          this.hasNewFeed = false;
        }
        this.selectFeedTag('All',reload);
      }else{
        this.getPropsForTag();
        trackEventOnGoogle('homeDiscover','tag',tag.k)
      }
    },
    handleCmtyTagClick:function() {
      this.curFixedEdmIndex = 0;
      this.curDisPlayEdmDate = '';
      this.fixDate = false;
      this.fixTabs = false;
      this.sortedDates = [];
      this.page = 0;
      this.cmtyProps = [];
      this.cmtyPropsObj = {};
      this.hasMoreProps = true;
    },
    getCmtyDetail:function() {
      var id = this.selectedCmty._id;
      if (!id) {
        return;
      }
      var body = {id};
      getStandardFunctionCaller()('findCmtyDetail',body,(err,ret)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        if (ret && !ret.msg) {
          this.selectedCmty = ret;
        }
      });
    },
    getPropsForTag: function() {
      if (!this.hasMoreProps) {
        return;
      }
      var url = '/1.5/props/search';
      var oneMonth = 2592000000;//1000*3600*24*30;
      var body = {
        page:this.page,
        tsDiff:oneMonth,
        isAllowedVipUser: false,
        ptype:'r',
        label: 1,
        no_mfee: false,
        soldOnly: true,
        oh: false,
        saletp:'Sale',
        sort:'auto-ts',
        src:'mls',
        skipFav:1,
      };
      if (this.propTag.k != "feed") {
        body[this.propTag.k] = this.propTag.v;
        if (this.propTag.k != 'ts' && this.propTag.v != 'new') {
          body.sort = 'spcts-desc';
        }
        if (this.propTag.k == 'lpChg'){
          body.sort = 'pcts-desc';
        }
        if (this.propTag.k == 'soldLoss') {
          body.status = 'U';
        }
      }
      if (this.selectedCmty._id) {
        body.cmty = this.selectedCmty.nm;
        body.city = this.selectedCmty.city_en;
        body.prov = this.selectedCmty.pr_en;
      } else {
        body.city = this.userCity.o;
        body.prov = this.userCity.p_ab;
      }
      const self = this;
      fetchData(url,{body},function(err,ret) {
        if (err) {
          console.log(err);
          return;
        }
        var props = ret.items;
        if (ret.cnt < 20 || props.length == 0) {
          self.hasMoreProps = false;
        } else {
          self.hasMoreProps = true;
        }
        self.cmtyProps = self.getDatePropsFormat(props);
      });
    },
    getDatePropsFormat:function(props) {
      var _cmtyPropsObj = Object.assign({},this.cmtyPropsObj);
      var dateTp = 'spcts';
      if (this.propTag.k == 'ts' && this.propTag.v == 'new') {
        dateTp = 'ts';
      }
      if (this.propTag.k == 'lpChg'){
        dateTp = 'pcts';
      }
      var dates = this.sortedDates;
      props.forEach(prop => {
        var date = this.formatUTC2Locale(prop[dateTp]||prop.ts||'');
        if (_cmtyPropsObj[date]) {
          _cmtyPropsObj[date].push(prop);
        } else {
          dates.push(date);
          _cmtyPropsObj[date] = [prop];
        }
      });
      this.cmtyPropsObj = _cmtyPropsObj;
      var cmtyProps = [];
      dates.sort().reverse()
      dates.forEach(date => {
        cmtyProps.push({id:date,props:_cmtyPropsObj[date]});
      });
      this.sortedDates = dates;
      return cmtyProps;
    },
    handleLoadMore:function(){
      var self = this, wait = 400;
      self.getScrollDirection();
      if (!self.waiting) {
        self.waiting = true;
        self.scrollLoading = true;
        setTimeout(function () {
          self.waiting = false;
          var element = self.scrollElement;
          if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 260)) {
            if (self.propTag.k != "feed") {
              self.page += 1;
              self.getPropsForTag();
            } else {
              self.getEdmProps();
            }
          }
          self.checkScrollAndSendLogger(element)
        }, wait);
      }
    },
    getDatePropsBlockId(propsBlock){
      return `${propsBlock.id}`;
    },
    goToTop() {
      this.curFixedEdmIndex = 0;
      this.curDisPlayEdmDate = '';
      this.fixDate = false;
      document.getElementById('content').scrollTop = 0 ;
    },
    scrollBar(){
      setTimeout(() => {
      var navLinksContainer = document.querySelector('.tabs-container');
      var navLinkSelected = document.querySelector('.selected');
      if (!navLinksContainer || !navLinkSelected ) {
        return setTimeout(() => {
          this.scrollBar()
        }, 200);
      }
      var windowWidth = window.innerWidth
      var boundData = navLinkSelected.getBoundingClientRect();
      var diffWidth = (windowWidth - boundData.width) / 2
      var targetOffset = navLinkSelected.offsetLeft - diffWidth;
      if (targetOffset < 0) {
        navLinksContainer.scrollLeft = 0;
        return
      }
      navLinksContainer.scrollLeft = targetOffset
      }, 20);
    },
    calcDirection:function(k,idx){
      let curK = this.propTag.k;
      // this.propTags
      // idx > cur -> l2r
      // index < cur -> r2l
      let lastK = this.lastPropTag.k;
      let curIdx = this.propTags.findIndex((e)=>{return e.k==curK})
      let lastIdx = this.propTags.findIndex((e)=>{return e.k==lastK})
      if(idx==curIdx){
        // console.log(lastIdx,idx)
        return lastIdx>idx?'r2l':'l2r';//last value
      }
      if(idx==lastIdx){
        return 'out'
      }
      return '';
    },
    getFixedDateClass() {
      var fixedDateClassArray = [];
      if (this.fixDate) {
        fixedDateClassArray.push('fixed');
      }
      if (this.propTag) {
        fixedDateClassArray.push('hasPropTag');
      }
      if (this.propTag.k == "feed"){
        fixedDateClassArray.push('feedTag');
      }
      return fixedDateClassArray.join(' ');
    },
    activeCity(){
      return this.selectedCmty.nm || this.userCity.n
    },
    goToDiscover(){
      trackEventOnGoogle('homeDiscover','discover')
      location.href = '/1.5/community/filter?d=/home/<USER>'
    }
  },
  destroyed() {
    this.clearTimer();
    if (RMSrv.offAppStateChange && this.appStateCallback) {
      RMSrv.offAppStateChange(this.appStateCallback);
    }
  },
};
var app = Vue.createApp(discover);
app.component('prop-item', propItem);
app.component('daily-feeds', dailyFeeds);
app.component('match-list-popup', MatchListPopup);
app.component('watching-popup', watchingPopup);
trans.install(app,{ref:Vue.ref})
app.mixin(pageDataMixins);
app.mount('#discover');