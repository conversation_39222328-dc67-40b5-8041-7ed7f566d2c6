###
LLM内容审核模块
使用大语言模型(LLM)进行文本和图片内容审核的过滤函数

配置示例:
  llmModeration:
    check: true
    bypass: ['normal']
    translatorList: ['gpt', 'claude', 'gemini']
    timeout: 30000

支持的违规标签:
normal: 正常内容
spam: 垃圾信息
ad: 广告
politics: 涉政
terrorism: 暴恐
abuse: 辱骂
porn: 色情
flood: 灌水
contraband: 违禁
meaningless: 无意义
customized: 自定义违规

Create date: 2025-07-14
Author: AI Assistant
###

debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
translatorManagerLib = require '../lib/translator/translatorManager'
llmHelper = require './llmTranslationHelper'

# 全局配置对象
config = null
translatorManager = null

# 模块配置
module.exports._config_sections = ['azure','deepseek','deepL','openAI','gemini','claude','grok','rm']
module.exports._config = (cfg) ->
  # 参数验证
  return unless cfg and typeof cfg is 'object'
  # 初始化LLM
  if not config
    config = Object.assign {check:true},cfg
  else
    # 更新现有配置
    Object.assign config, cfg
  translatorManager ?= translatorManagerLib.createTranslatorManager(cfg)

  config.bypass ?= []

# 暴露内部变量供测试使用
module.exports._getTranslatorManager = -> translatorManager
module.exports._setTranslatorManager = (tm) -> translatorManager = tm

###*
 * 格式化审核响应结果
 * @param {Boolean} passed - 是否通过审核
 * @param {Array} labels - 违规标签列表
 * @param {String} reason - 违规原因
 * @param {String} content - 原始内容
 * @param {Object} options - 选项参数
 * @returns {Object} 格式化的响应 {block: boolean, labels: Array, msg: string}
###
formatModerationResponse = (passed, labels, reason, content, options = {}) ->
  # 应用bypass配置
  bypass = (config.bypass or []).concat(options.bypass or [])
  filteredLabels = []
  
  for label in labels
    if bypass.indexOf(label) < 0
      filteredLabels.push {
        label: label
        content: content
        details: {reason: reason}
      }
  
  block = not passed and filteredLabels.length > 0
  msg = l10nContentViolation(filteredLabels, options.l10n)
  
  return {block, labels: filteredLabels, msg}

###*
 * 国际化违规内容信息
 * @param {Array} violations - 违规信息列表
 * @param {Function} l10n - 国际化函数
 * @param {String} msg - 消息模板
 * @param {String} sepChar - 分隔符
 * @returns {String} 国际化后的消息
###
l10nContentViolation = (violations, l10n, msg = 'Your submission has %s contents', sepChar = '/') ->
  return '' if violations.length is 0
  
  labels = []
  contexts = []
  
  for v in violations
    label = if l10n then l10n(v.label, 'policy') else v.label
    labels.push label
    
    if v.details?.reason
      contexts.push v.details.reason
  
  msg = if l10n then l10n(msg) else msg
  finalMsg = msg.replace('%s', labels.join(sepChar))
  
  if contexts.length > 0
    finalMsg += ": #{contexts.join(', ')}"
  
  return finalMsg

###*
 * 使用LLM进行文本内容审核
 * @param {String|Array} content - 要审核的文本内容（字符串或字符串数组）
 * @param {Array} promptTemplates - 提示词模板数组
 * @param {Object} options - 可选参数 {bypass: Array, l10n: Function, translatorList: Array, timeout: Number}
 * @returns {Promise<Object>} 审核结果 {block: boolean, labels: Array, msg: string}
###
module.exports.textModerationLLM = (content, promptTemplates, options = {}) ->
  # 参数验证
  return {block: false, labels: [], msg: ''} unless config?.check
  return {block: false, labels: [], msg: ''} unless content
  return {block: false, labels: [], msg: ''} unless promptTemplates and Array.isArray(promptTemplates) and promptTemplates.length > 0
  return {block: false, labels: [], msg: ''} unless translatorManager

  # 处理数组内容
  if Array.isArray(content)
    return {block: false, labels: [], msg: ''} if content.length is 0
    # 对于数组，合并所有内容进行审核，过滤掉空值和纯空白字符串
    filteredContent = content.filter((item) -> item and typeof item is 'string' and item.trim())
    return {block: false, labels: [], msg: ''} if filteredContent.length is 0
    combinedContent = filteredContent.join('\n')
  else
    combinedContent = content

  # 调用translatorManager的filterContent方法进行审核
  try
    filterResult = await translatorManager.filterContent(
      combinedContent,
      promptTemplates,
      {
        context: 'text'
        filterType: 'comment_filter'
      }
    )
  catch error
    debug.error 'LLM文本审核失败:', error
    # 审核失败时默认不阻止内容
    return {block: false, labels: [], msg: ''}

  # 检查过滤结果
  unless filterResult.success
    debug.error 'LLM文本审核失败:', filterResult.error
    # 审核失败时默认不阻止内容
    return {block: false, labels: [], msg: ''}

  # 根据过滤结果生成标签
  labels = if filterResult.passed then [] else ['customized']

  # 格式化返回结果
  return formatModerationResponse(
    filterResult.passed,
    labels,
    filterResult.reason,
    combinedContent,
    options
  )

###*
 * 使用LLM进行图片内容审核
 * @param {String} imageUrl - 图片URL
 * @param {Array} promptTemplates - 提示词模板数组
 * @param {Object} options - 可选参数 {bypass: Array, l10n: Function, translatorList: Array, timeout: Number}
 * @returns {Promise<Object>} 审核结果 {block: boolean, labels: Array, msg: string}
###
module.exports.imageModerationLLM = (imageUrl, promptTemplates, options = {}) ->
  # 参数验证
  return {block: false, labels: [], msg: ''} unless config?.check
  return {block: false, labels: [], msg: ''} unless imageUrl
  return {block: false, labels: [], msg: ''} unless promptTemplates and Array.isArray(promptTemplates) and promptTemplates.length > 0
  return {block: false, labels: [], msg: ''} unless translatorManager

  # 构建图片输入对象
  imageInput = {
    type: 'image'
    url: imageUrl
  }

  # 调用translatorManager的filterContent方法进行审核
  try
    filterResult = await translatorManager.filterContent(
      imageInput,
      promptTemplates,
      {
        context: 'image'
        filterType: 'comment_filter'
      }
    )
  catch error
    debug.error 'LLM图片审核失败:', error
    # 审核失败时默认不阻止内容
    return {block: false, labels: [], msg: ''}

  # 检查过滤结果
  unless filterResult.success
    debug.error 'LLM图片审核失败:', filterResult.error
    # 审核失败时默认不阻止内容
    return {block: false, labels: [], msg: ''}

  # 根据过滤结果生成标签
  labels = if filterResult.passed then [] else ['customized']

  # 格式化返回结果
  return formatModerationResponse(
    filterResult.passed,
    labels,
    filterResult.reason,
    imageUrl,
    options
  )
    
