#!/bin/bash
# Check some apis and read data processStatus, it will notify by email if any warning or error found
# ./batch/checkServerStatus.sh -a http://app.test:8080 -w http://www.test:8080 -e <EMAIL>
# ./batch/checkServerStatus.sh -a http://realmaster.com -w http://www.realmaster.com -e <EMAIL>
# Options:
#   -a app's url without language and the last / (http://e1w.realmaster.com)
#   -w web's url
#   -e email address to recive notification
# Author:         Louis
# Run duration:   less than a minuet
# Run frequency:  depends
APP_COOKIE=apsv=appDebug
LANGUAGE_COOKIE=locale=en
RM_URL=https://ml1.realmaster.cc/send
RM_FROM=<EMAIL>
TWO_DAYS_IN_SECONDS=172800
CITY_SEARCH='{
  "label": 1,
  "page": 0,
  "src": "mls",
  "saletp": "sale",
  "city": "Toronto",
  "prov": "ON",
  "ptype": "Residential",
  "no_mfee": false,
  "sort": "auto-ts",
  "oh": false,
  "ts": 1633099784283
}'

MAP_SEARCH='{
  "label": 1,
  "page": 0,
  "bbox": [
      -79.44113347879379,
      43.510618867050795,
      -79.18364141335947,
      43.85127125096233
  ],
  "src": "mls",
  "saletp": "sale",
  "ptype": "Residential",
  "no_mfee": false,
  "sort": "auto-ts",
  "oh": false,
  "soldOnly": true,
  "ts": 1632948144647
}'

rmText=''
firstPropertyId=''

usage()
{
  echo ""
  echo "Usage: $0 -a app domain -w web domain"
  echo -e "\t-a app domain eg. http://app.test:8082"
  echo -e "\t-w web domain eg. http://d7ww.realmaster.com:8082"
  echo -e "\t-e email address eg. <EMAIL>,<EMAIL>"
  exit 1 # Exit script after printing help
}


while getopts "a:e:w:" option; do
  case "${option}" in
    a)
        APP_URL=${OPTARG}
        ;;
    w)
        WEB_URL=${OPTARG}
        ;;
    e)
        RM_TO=${OPTARG}
        ;;
    *)
        usage
        ;;
  esac
done

if [ -z "$APP_URL" ] && [ -z "$WEB_URL" ]; then
  echo "(-a or -w) and -e are required";
  usage
fi

# remove last / of url
APP_URL=$(echo $APP_URL | sed -e 's/\/$//')
WEB_URL=$(echo $WEB_URL | sed -e 's/\/$//')

echo $APP_URL
echo $WEB_URL


if [ "$APP_URL" ]; then
  # Check app's homepage
  # Check if a.drawer ( items in transformer)exist
  echo `date`
  echo "======================= Checking homepage ======================="
  homepageRaw=$(curl -s -i -L \
    --request GET \
    --cookie "$LANGUAGE_COOKIE;$APP_COOKIE" \
    $APP_URL/home/<USER>
  homeStatus=$(echo "$homepageRaw" | grep "^HTTP\/")
  transformers=$(echo "$homepageRaw" | grep "drawer" | grep "resale")

  if [ "$transformers" ]; then
    echo "normal: Homepage is normal"
  else
    echo "error: Homepage is down, no a.drawer.textContent === resale found"
    rmText="$rmText <p>error: Homepage is down, no a.drawer found</p>"
  fi


  # City
  # POST /1.5/props/search and check id result.cnt > 0
  # Store property id for later use
  echo "======================= Checking /1.5/props/search (city) ======================="
  # post request
  # TODO: shf1 ERR@2024-02-02T13:03:55.762:Unexpected end of JSON input, local test passed
  propertyListRaw=$(curl --request POST \
    -s -L \
    --header "Content-Type: application/json" --cookie $APP_COOKIE \
    --data "$CITY_SEARCH"\
    $APP_URL/1.5/props/search)
  # check if return is valid json, and select .cnt
  if jq -e . >/dev/null 2>&1 <<< "${propertyListRaw}"; then
    propertyListCounts=$(jq -r 'select(has("cnt")) | .cnt' <<< "${propertyListRaw}")
    # firstPropertyId will be used when checking detail
    firstPropertyId=$(jq -r '.items | first | ._id' <<< "${propertyListRaw}")
    # check if .cnt greater than 0
    if [ $propertyListCounts ] && [ $propertyListCounts -gt 0 ]; then
      echo 'normal: Property list is normal'
    else
      echo 'error: property list is down, no .cnt found'
      rmText="$rmText <p>error: Property list is down, no .cnt found</p>"
    fi
  else
    echo 'error: property list is down, server error or invalid json'
    rmText="$rmText <p>error: Property list is down, server error or invalid json</p>"
  fi

  # Map search
  # POST /1.5/props/search and check id result.cnt > 0
  echo "======================= Checking /1.5/props/search (map) ======================="
  # post resuest
  mapSearchRaw=$(curl -X POST --header "Content-Type: application/json" \
    -s -L \
    --cookie $APP_COOKIE \
    --data "$MAP_SEARCH"\
    $APP_URL/1.5/props/search)
  # check if json valid
  if jq -e . >/dev/null 2>&1 <<< "${mapSearchRaw}"; then
    # check cnt
    mapSearchCounts=$(echo "$mapSearchRaw" | jq -r 'select(has("cnt")) | .cnt')
    if [ $mapSearchCounts ] && [ $mapSearchCounts -gt 0 ]; then
      echo 'normal: Map search is normal'
    else
      echo 'error: Map search is down, .cnt is missing'
      rmText="$rmText <p>error: Map search list is down, .cnt is missing</p>"
    fi
  else
    echo 'error: Map search is down, server error or invalid json'
    rmText="$rmText <p>error: Map search is down, server error or invalid json</p>"
  fi


  # Property detail
  # POST /1.5/props/detail and check if price, address, id exist
  echo "======================= Checking /1.5/props/detail ======================="
  # firstPropertyId=TRBC5647386 {"ok":0,"id":"TRBC5647386","cnt":0,"e":"According to Real Estate Board rules, you have to login to see this listing!","ec":1}
  echo "$firstPropertyId"
  if [ -z "$firstPropertyId" ]; then
    echo 'error: Property detail is down, No property id found in listing'
    rmText="$rmText <p>error: Property detail is down, No property id found</p>"
  else
    payload=$(jq -n \
    --arg locale "en" \
    --arg _id $firstPropertyId \
    '{_id: $_id, locale: $locale}')
    propertyDetailRaw=$(curl -X POST --header "Content-Type: application/json" \
      -s -L \
      --cookie $APP_COOKIE \
      --data "$payload"\
      $APP_URL/1.5/props/detail) #| \
    #jq '.prop | [has("_id"), has("addr"), has("e")] | any')
    # echo "$propertyDetailRaw"
    # .detail?
    if jq -e . >/dev/null 2>&1 <<< "${propertyDetailRaw}"; then
      propertyDetail=$(echo "$propertyDetailRaw"| jq '.prop | [has("_id"), has("addr"), has("e")] | any')
      if [ "$propertyDetail" ]; then
        echo 'normal: Property detail is normal'
      else
        echo 'error: Property detail is down, no _id && addr && lp found'
        rmText="$rmText <p>error: Property detail is down, no _id && addr && lp found</p>"
      fi
    else
      echo 'error: Property detail is down, server error or invalid json'
      rmText="$rmText <p>error: Property detail is down, server error or invalid json</p>"
    fi
  fi


  # Forum
  # POST /1.5/forum/query and check the latest mt, should have new post at least every two days
  echo "======================= Checking /1.5/forum/query  ======================="
  # post request
  lastPostRaw=$(curl -X POST --header "Content-Type: application/json" \
    -s -L \
    --cookie $APP_COOKIE \
    --data '{ }'\
    $APP_URL/1.5/forum/query)
  # check json
  if jq -e . >/dev/null 2>&1 <<< "${lastPostRaw}"; then
    lastPost=$(jq -r ".forums | max_by(.mtInSeconds) | .mtInSeconds" <<< "${lastPostRaw}")
    if [ $lastPost ] && [ $lastPost -gt 0 ]; then
      now=$(date +%s)
      different=$(($now - $lastPost))

      if [ $different -gt $TWO_DAYS_IN_SECONDS ]; then
        echo 'waring: Forum, No new post for two days'
        rmText="$rmText <p>waring: Forum, No new post for two days</p>"
      else
        echo 'normal: Forum is normal'
      fi
    else
      echo 'error: Forum is down, .mtInSeconds is missing'
      rmText="$rmText <p>error: Forum is down, .mtInSeconds is missing</p>"
    fi
  else
    echo 'error: Forum is down, server error or invalid json'
    rmText="$rmText <p>error: Forum is down, server error or invalid json</p>"
  fi
fi

if [ "$WEB_URL" ]; then
  # Import batches
  # POST /proccess and show results
  echo "======================= Checking /proccess  ======================="
  serverStatus=$(curl -X POST \
    -s -L \
    --header "secret:S-qq8vm" \
    $WEB_URL/proccess)
  # check json
  # echo $serverStatus
  if jq -e . >/dev/null 2>&1 <<< "${serverStatus}"; then
    if [ "$serverStatus" ]; then
      normals=$(jq -r '.status | map(select(.status == "normal")) | .[] | @base64' <<< "${serverStatus}")
      messages=$(jq -r '.status | map(select((.status == "warning") or (.status == "error"))) | .[] | @base64 ' <<< "${serverStatus}")
    fi
    if [ -z "$normals" ] && [ -z "$messages" ]; then
      echo 'warning: processStatus collection is empty'
      rmText="$rmText <p>warning:  processStatus collection is empty</p>"
    else
      for normal in $normals; do
        _jq() {
          echo ${normal} | base64 --decode | jq -r ${1}
        }
        echo $(_jq '.message')
      done
      for message in $messages; do
        _jq() {
          echo ${message} | base64 --decode | jq -r ${1}
        }
        echo $(_jq '.message')
        rmText="$rmText <p>$(_jq '.message')</p>"
      done
    fi
  else
    echo 'error: processStatus, server error or invalid json'
    rmText="$rmText <p>error: processStatus, server error or invalid json</p>"
  fi
fi

# Send email (rmMail) if #rmText > 0
# exit 0
if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= Send email =======================\n"
  rmText="$rmText <p>APP_URL: $APP_URL</p> <p>WEB_URL: $WEB_URL</p>"
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "Status" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  # split .to to array by ','
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}" )
  curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$mail"\
    $RM_URL

fi
echo `date`
echo "======================= Finished ======================="

exit