###
Description: 批量翻译Forum表中的记录，包括帖子内容和评论信息
功能：
1. 分开缓存prompts模版中scenario为'forum_translation','comment_translation', status为'active'的记录
2. 查找Forum中的所有记录
3. 遍历记录，使用Forum模型的translateForumPost和translateComment函数进行翻译

Options:
    -d --date:        date, format YYYY-MM-DD, 处理此日期之后的记录
    -l --limit:       number, 限制处理的记录数量（用于测试）

usage:         ./start.sh -n translate_forum -cmd "lib/batchBase.coffee batch/forum/translate_forum.coffee preload-model dryrun"
createDate:    2025-07-24
Author:        Luo xiaowei
Run frequency: As needed
###

debug = DEBUG()
helpers = INCLUDE 'lib.helpers'
speed = INCLUDE 'lib.speed'
yargs = require('yargs')

# 导入必要的模型
ForumModel = MODEL 'Forum'
PromptsModel = MODEL 'Prompts'

# 获取数据库集合
ForumCol = COLLECTION 'chome', 'forum'

# 命令行参数配置
yargs = require('yargs')(AVGS)
yargs.option 'date', {
  alias: 'd',
  description: '处理此日期之后的记录，格式：YYYY-MM-DD',
  required: false,
  type: 'string'
}
yargs.option 'limit', {
  alias: 'l',
  description: '限制处理的记录数量（用于测试）',
  required: false,
  type: 'number'
}

START_DATE = yargs.argv.date
RECORD_LIMIT = yargs.argv.limit

# 干运行模式检查
dryRun = (AVGS.indexOf('dryrun') >= 0) or DRYRUN

# 性能监控器配置
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 全局变量
forumTranslationTemplates = []
commentTranslationTemplates = []
startTs = new Date()

###
初始化翻译模板
获取forum_translation和comment_translation的活跃模板
###
initializeTemplates = () ->
  try
    # 获取论坛翻译模板
    forumTranslationTemplates = await PromptsModel.getCachedPromptTemplates('forum_translation', 'active')
    debug.info "获取到 #{forumTranslationTemplates.length} 个论坛翻译模板"
    
    # 获取评论翻译模板
    commentTranslationTemplates = await PromptsModel.getCachedPromptTemplates('comment_translation', 'active')
    debug.info "获取到 #{commentTranslationTemplates.length} 个评论翻译模板"
  catch error
    debug.error "获取翻译模板失败:", error
    return false
  
  return true

###
构建查询条件
根据命令行参数构建数据库查询条件
###
buildQuery = () ->
  query = {}
  
  # 只查询有内容的记录
  query.$or = [
    {m: {$exists: true, $ne: ''}},  # 有帖子内容
    {tl: {$exists: true, $ne: ''}}, # 有标题
    {cmnts: {$exists: true, $ne: []}} # 有评论
  ]
  
  # 日期过滤
  if START_DATE
    query.ts = {$gte: new Date(START_DATE)}
    debug.info "只处理 #{START_DATE} 之后的记录"
  
  return query

###
主函数
执行批量翻译任务
###
main = () ->
  # 初始化翻译模板
  templatesReady = await initializeTemplates()
  unless templatesReady
    debug.error "翻译模板初始化失败，退出程序"
    return EXIT 1, "翻译模板初始化失败"
  
  # 检查是否有可用的翻译模板
  if (forumTranslationTemplates.length is 0) and (commentTranslationTemplates.length is 0)
    debug.warn "没有找到任何翻译模板，退出程序"
    return EXIT 0, "没有找到翻译模板"
  
  # 构建查询条件
  query = buildQuery()
  debug.info '查询条件:', JSON.stringify(query)
  
  # 设置查询选项
  projection = {
    _id: 1, gid: 1, m: 1, tl: 1, cmnts: 1, ts: 1, lang: 1
  }
  
  options = {projection}
  if RECORD_LIMIT
    options.limit = RECORD_LIMIT
    debug.info "限制处理记录数量: #{RECORD_LIMIT}"
    
  cur = await ForumCol.find(query, options)
  stream = cur.stream()
  
  streamObj =
    verbose: 1
    high: 1
    stream: stream
    end: (err) ->
      if err
        debug.error '处理Forum 时发生错误:', err
        return EXIT 1, err
      
      processTs = (new Date().getTime() - startTs.getTime()) / (1000 * 60)
      debug.info "处理完成，耗时 #{processTs.toFixed(2)} 分钟"
      debug.info '最终统计:',speedMeter.toString()
      return EXIT 0
    process: (post, cb) ->
      speedMeter.check {processed: 1}
      postId = post._id
      gid = post.gid
      
      # 翻译帖子内容（标题和正文）
      if (forumTranslationTemplates.length > 0) and (post.m or post.tl)
        if dryRun
          debug.info "DryRun: 将翻译帖子 #{postId}, #{post.tl}"
        else
          try
            await ForumModel.translateForumPost({
              postId: postId,
              gid: gid,
              promptTemplates: forumTranslationTemplates
            })
            debug.info "成功翻译帖子: #{postId}"
            speedMeter.check {forumTranslated: 1}
          catch error
            debug.error '翻译帖子失败:', postId, error
            speedMeter.check {forumFailed: 1}
      
      # 翻译评论
      if (commentTranslationTemplates.length > 0) and post.cmnts and Array.isArray(post.cmnts)
        commentsTranslated = 0
        for comment, index in post.cmnts
          # 只翻译有内容的评论
          if comment.m and comment.m.trim() isnt ''
            # 确定源语言，优先使用comment.lang，否则使用默认值
            if comment.lang
              sourceLanguage = comment.lang
            else
              sourceLanguage = if (helpers.hasChinese(comment.m)) then 'zh-cn' else 'en'
            if dryRun
              debug.info "DryRun: 将翻译评论 #{postId} #{post.tl}, index: #{index}"
            else
              try
                await ForumModel.translateComment({
                  id: postId,
                  commentIndex: index,
                  sourceLanguage: sourceLanguage,
                  translationTemplates: commentTranslationTemplates,
                  gid: gid
                })
                commentsTranslated++
              catch error
                debug.error '翻译评论失败:', postId, index, error
                speedMeter.check {commentFailed: 1}
        if commentsTranslated > 0
          debug.info "成功翻译帖子 #{postId} #{post.tl}, 的 #{commentsTranslated} 条评论"
          speedMeter.check {commentsTranslated: commentsTranslated}
      
      return cb()

  helpers.streaming streamObj

# 启动主函数
main()
