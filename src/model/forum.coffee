###
Forum Model
###
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
cityHelper = INCLUDE 'lib.cityHelper'
{JT2FT,FT2JT,JTFTAUTO} = INCLUDE 'lib.i18n'
{hasChinese} = INCLUDE 'lib.helpers_string'
ObjectId = INCLUDE('lib.mongo4').ObjectId
functionHelper = INCLUDE 'lib.helpers_function'
PnToken = COLLECTION 'chome','pn_token'

wordpressHelper = INCLUDE 'libapp.wordpressHelper'
{removeIRM2REImagePathCheckDataFormat,replaceRM2REImagePathCheckDataFormat} = INCLUDE 'libapp.propertyImage'
{addWpComment,deleteWpComment,getTagsByCategoryIds} = wordpressHelper
{isPropObjectId} = INCLUDE 'libapp.properties'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'

UserModel = MODEL 'User'

# 全局常量定义
DRAFT_EXPIRY_MS = 365 * 24 * 60 * 60 * 1000 # 365天的毫秒数

ForumCol = COLLECTION 'chome','forum'
GroupForumCol = COLLECTION 'chome','group_forum'
config = CONFIG(['serverBase','azure','deepseek','deepL','openAI','gemini','claude','grok','rm'])
if config.serverBase?.masterMode
  ForumCol.createIndex {'rank':-1,'sticky':-1,'mt': -1}
  ForumCol.createIndex {'favs':1}
  ForumCol.createIndex {'cmnts.uid':1}
  ForumCol.createIndex {'wpHosts.wpid':1,'wpHosts.wpHost':1}
  ForumCol.createIndex {'fornm':1}
  ForumCol.createIndex {'reno':1}
  ForumCol.createIndex {'src':1}
  # 新增：TTL index for draft expiration (只使用 exp 字段)
  ForumCol.createIndex {'exp':1},{expireAfterSeconds:0}
  GroupForumCol.createIndex {'sticky':-1, 'mt': -1}
  GroupForumCol.createIndex {'favs':1}
  GroupForumCol.createIndex {'cmnts.uid':1}
  GroupForumCol.createIndex {'fornm':1}
  GroupForumCol.createIndex {'gid':1}
  GroupForumCol.createIndex {'readers.uid':1,'readers.status':1}
  GroupForumCol.createIndex {'src':1}
  # 新增：TTL index for group forum draft expiration
  GroupForumCol.createIndex {'exp':1},{expireAfterSeconds:0}

# 初始化翻译管理器
translatorManager = translatorManagerLib.createTranslatorManager(config)

# 评论翻译相关常量和辅助函数
COMMENT_LANGUAGE_MAPPING = {
  'zh-cn': 'zh_cn'
  'zh': 'zh'
  'en': 'en'
  'kr': 'kr'
}

SUPPORTED_COMMENT_LANGUAGES = ['zh_cn', 'zh', 'en', 'kr']

displayOtherLanguage = (item, userLocale) ->
  return unless item and userLocale

  for k,v of COMMENT_LANGUAGE_MAPPING
    if k isnt userLocale
      delete item["m_#{v}"]
      delete item["tl_#{v}"]  # 添加对tl多语言字段的处理

  return

###
处理单个内容项的多语言显示
根据用户语言偏好返回对应的翻译内容
@param {Object} item - 内容项（forum 或 comment）
@param {String} userLocale - 用户语言偏好
@return {Object} 处理后的内容项
###
processMultilingualContent = (item, userLocale) ->
  return item unless item and userLocale

  # 隐藏其他语言
  displayOtherLanguage(item, userLocale)

  # 处理多语言翻译
  userLangField = COMMENT_LANGUAGE_MAPPING[userLocale]
  return item unless userLangField

  # 如果用户语言与源语言一致，直接返回
  if item.lang is userLocale
    return item

  # 处理内容字段 (m)
  translatedFieldName = "m_#{userLangField}"
  translatedContent = item[translatedFieldName]

  if translatedContent and (translatedContent.trim() isnt '')
    item.origM = item.m unless item.origM  # 保存原始内容（如果还没保存）
    item.m = translatedContent  # 设置翻译内容
    delete item[translatedFieldName]

  # 处理标题字段 (tl)
  translatedTitleFieldName = "tl_#{userLangField}"
  translatedTitle = item[translatedTitleFieldName]

  if translatedTitle and (translatedTitle.trim() isnt '')
    item.origTl = item.tl unless item.origTl  # 保存原始标题（如果还没保存）
    item.tl = translatedTitle  # 设置翻译标题
    delete item[translatedTitleFieldName]

  return item

###
处理评论数组的多语言显示
@param {Array} comments - 评论数组
@param {String} userLocale - 用户语言偏好
@return {Array} 处理后的评论数组
###
processMultilingualComments = (comments, userLocale) ->
  return comments unless comments and Array.isArray(comments) and userLocale

  for comment in comments
    processMultilingualContent(comment, userLocale)

  return comments

###
检查评论中缺失的翻译字段
@param {Object} comment - 评论对象
@param {String} sourceLang - 源语言代码
@return {Array} 缺失的语言代码数组
###
getMissingCommentLanguages = (comment, sourceLang) ->
  targetLanguages = []
  for k,v of COMMENT_LANGUAGE_MAPPING
    if k is sourceLang
      continue
    fieldName = "m_#{v}"
    if (not comment[fieldName]) or (comment[fieldName].trim() is '')
      targetLanguages.push k
  return targetLanguages

###
获取评论在指定语言下的内容
@param {Object} comment - 评论对象
@param {String} targetLang - 目标语言代码
@return {String} 对应语言的评论内容
###
getCommentInLanguage = (comment, targetLang) ->
  # 如果目标语言就是源语言，直接返回原文
  return comment.m if comment.lang is targetLang

  # 否则返回翻译字段，如果不存在则降级到原文
  fieldName = "m_#{targetLang}"
  return comment[fieldName] or comment.m

###
翻译单个评论内容到指定语言
@param {String} content - 原文内容
@param {String} sourceLang - 源语言代码
@param {String} targetLang - 目标语言代码
@param {Array} templates - 翻译模板
@return {Promise<Object>} 翻译结果 {success: boolean, translatedText: string, error: string}
###
translateCommentToLanguage = (content, sourceLang, targetLang, templates, options) ->
  # zh-cn ↔ zh 使用 JTFTAUTO
  if (sourceLang is 'zh_cn' and targetLang is 'zh') or
      (sourceLang is 'zh' and targetLang is 'zh_cn')
    targetLocale = if targetLang is 'zh_cn' then 'zh_cn' else 'zh'
    translatedText = JTFTAUTO(content, targetLocale)
    return {success: true, translatedText: translatedText}

  # 其他语言使用 LLM 翻译
  if templates and templates.length > 0
    # 将内部语言代码转换为显示用的语言代码
    displaySourceLang = if sourceLang is 'zh_cn' then 'zh-cn' else sourceLang
    displayTargetLang = if targetLang is 'zh_cn' then 'zh-cn' else targetLang

    options.sourceLanguage = displaySourceLang

    try
      result = await translatorManager.translateContent(content, displayTargetLang, templates, options)
      return result
    catch error
      debug.error "翻译评论内容失败 (#{sourceLang} -> #{targetLang}):", error
      return {success: false, error: error.message}
  else
    return {success: false, error: '没有可用的翻译模板'}


###
批量翻译评论到多个目标语言
@param {String} content - 原文内容
@param {String} sourceLang - 源语言代码
@param {Array} targetLanguages - 目标语言数组
@param {Array} templates - 翻译模板
@return {Promise<Object>} 翻译结果映射 {language: translatedText}
###
translateCommentToLanguages = (content, sourceLang, targetLanguages, templates, options) ->
  translations = {}

  # 并行翻译所有目标语言
  translationPromises = targetLanguages.map (targetLang) ->
    translateCommentToLanguage(content, sourceLang, targetLang, templates, options)
      .then (result) ->
        if result.success
          translations[targetLang] = result.translatedText
        else
          debug.error "翻译到#{targetLang}失败:", result.error
        return result
      .catch (error) ->
        debug.error "翻译到#{targetLang}异常:", error
        return {success: false, error: error.message}

  try
    await Promise.all(translationPromises)
    return translations
  catch error
    debug.error "批量翻译评论失败:", error
    return translations  # 返回部分成功的翻译结果

getFullProvName = (ret={})->
  if ret?.prov
    ret.prov = cityHelper.getProvFullName ret.prov
  if ret?.cnty
    ret.cnty = cityHelper.getCntyFullName ret.cnty
handleDBRet = (err,ret,cb)->
  if err
    debug.critical err
    return cb(err or MSG_STRINGS.DB_ERROR)
  cb null, ret

buildQuery = (
  {
    search #search url which contains forum id
    user
    isForumAdmin
    hasRoleRealtor
    src
    gid
    uid
    tag
    sticky
    reno
    subcate
    city
    prov
    cnty
    category
    yellowpage
    author
    gids
    isGroupForum
}) ->
  query = {}
  if search
    search = search.trim()
    #5f1ef46f6ab23f34112763d1
    if isPropObjectId(search)
      query._id = search
    else
      #http://app.test:8082/1.5/topics/details?share=1&id=5f1ef46f6ab23f34112763d1&lang=en
      [domain, queryString] = search.split('?')
      if queryString
        queryArray = queryString.split('&')
        for queryItem in queryArray
          if queryItem
            [key,value] = queryItem.split('=')
            if key is 'id'
              query._id = value
  if src
    query.src = if 'sch' is src then {$in: ['sch','psch']} else src
  query.uid = author if author
  query.tags = tag if tag
  query.sticky = {$in: sticky} if sticky
  query.reno = reno.toString() if reno
  # 修改：使用 exp 字段判断 draft 状态，只查询非 draft 的帖子
  query.exp = {$exists: false}
  if 'news' is src
    query.sticky={$ne:'global'}
  if 'post' is src # caused by previous bug, some post do not have src. so use fornm for now.
    delete query.src
    query.fornm ={$exists:true}
  sort = { 'rank': -1, 'sticky': -1, 'mt': -1 } #'adIntop':-1, 'adInBtop':-1,

  if yellowpage = yellowpage
    query.category = if 'all' is yellowpage then {$exists: true} else yellowpage
    query.subcate = {$in: subcate} if subcate?.length

  if not yellowpage
    query.city = city if city
    query.prov = cityHelper.getProvAbbrName prov if prov
    query.cnty = cityHelper.getCntyAbbrName cnty if cnty

  if reno
    sort = {'reno':-1, 'mt': -1}
  if not isForumAdmin
    query = Object.assign(query, {del:{$ne:true}})
    if not hasRoleRealtor
      query = Object.assign(query, {realtorOnly:{$exists:false}})

  query.tp = {$exists: true, $nin:[null,'']} if 'topic' is category
  query.uid = user._id.toString() if ('my_post' is category) and user
  query.favs = user._id if ('my_favourite' is category) and user
  if ('my_draft' is category) and user
    query.exp = {$exists: true, $gt: new Date()}
    query.uid = user._id if not isForumAdmin
  if ('my_group' is category) or gid
    query.gid = if gid then new ForumCol.ObjectId(gid) else {$in:gids}
  else
    query.blkUids = {$nin:[user._id]} if user?._id
  if isGroupForum
    query.gid = {$in:gids}
    query.$or = [
      {
        readers: {
          $elemMatch: {
            uid: user._id,
            status:{$ne:'read'}
          }
        }
      },
      {
        'readers.uid': {$ne:user._id}
      }
    ]
  query = Object.assign(query, {'cmnts.uid': user._id}) if 'my_reply' is category
  return query

sortCmnt = (cmnt1, cmnt2) ->
  if !cmnt1.del and cmnt2.del
    return -1
  if cmnt1.del and !cmnt2.del
    return 1
  if cmnt1.sticky and !cmnt2.sticky
    return -1
  if !cmnt1.sticky and cmnt2.sticky
    return 1
  if cmnt1.sticky and cmnt2.sticky
    return new Date(cmnt2.mt) - new Date(cmnt1.mt)
  return new Date(cmnt2.ts) - new Date(cmnt1.ts)

resetCmntl = (ret, index, set, del, unsticky) ->
  return unless ret?.cmnts?.length
  ret.cmnts[index].del = del unless del == null
  ret.cmnts[index].sticky = false if unsticky
  ret.cmnts.sort(sortCmnt)
  if ret.cmnts[0].del
    set.cmntl = ''
  else
    set.cmntl = ret.cmnts[0].m.substr(0,20)

setCmntThumbUpAndRating = (ratings, user, cmnt, cmntidx)->
  cmnt.tupcnt = cmnt.tupcnt or 0
  unless user and (ratings?.length)
    cmnt.tup = false
    return
  for rate in ratings
    if rate.uid.toString() is cmnt.uid.toString()
      cmnt.r = rate.r
    if rate.uid.toString() is user._id.toString()
      if rate[cmntidx.toString()] is true
        cmnt.tup = true
      else
        cmnt.tup = false

processCnmts = (post, user)->
  if post.cmnts
    for cmnt,idx in post.cmnts
      cmnt.sticky = cmnt.sticky or false
      cmnt.del = cmnt.del or false
      setCmntThumbUpAndRating post.rating, user, cmnt, idx

getCol = (gid)->
  if gid in ['undefined','null','false']
    gid = null
  return if gid then GroupForumCol else ForumCol

convertToGMT = (str)->
  return new Date() unless str
  if str.indexOf('z') < 0
    str = str+'z'
  return new Date(str)

getFullProv = (forum)->
  forum.prov = cityHelper.getProvFullName {prov:forum.prov} if forum.prov


class Forum
  @LIST_FIELDS = 
    history: 0
    news_src: 0
    news_chnl: 0
    news_url: 0
    photos: 0
    favs: 0
  # [forum] getForumList
  @findList:(
  {
    search #forum url or forum id
    locale
    user
    isForumAdmin
    hasRoleRealtor
    src
    gid
    uid
    tag
    sticky
    reno
    subcate
    city
    prov
    cnty
    category
    yellowpage
    page
    author
    gids
    isGroupForum
  }, cb)->
    fields = Object.assign({}, @LIST_FIELDS)
    unless 'topic' is category
      fields.m = 0
      fields.m_zh = 0
      fields.m_zh_cn = 0
      fields.m_en = 0
      fields.m_kr = 0
    # 取消通过adintop和adinbtop字段进行排序，添加rank字段，通过rank判断排列顺序，数值(1-5)高就在前面显示
    sort = { 'rank': -1, 'sticky': -1, 'mt': -1 } #'adIntop':-1, 'adInBtop':-1,
    page = parseInt page or 1
    limit = 20
    skip = (page-1)*limit

    query = buildQuery {
      search
      user
      isForumAdmin
      hasRoleRealtor
      src
      gid
      uid
      tag
      sticky
      reno
      subcate
      city
      prov
      cnty
      category
      yellowpage
      page
      author
      gids
      isGroupForum
    }
    if ('my_group' is category) or query.gid
      Col = GroupForumCol
      sort = {'gid': -1, 'sticky':-1,'mt': -1}
    else
      Col = ForumCol
    projection = {fields,sort, skip, limit: limit + 1}
    Col.findToArray query, projection, (err, forums) ->
      return cb(err,null) if err
      uids = []
      for forum in forums
        uid = forum.uid?.toString()
        uids.push(uid) if (uid and (uids.indexOf(uid) is -1))
        if forum.thumb
          forum.thumb = replaceRM2REImagePathCheckDataFormat(forum.thumb)
        # getFullProv forum
      return cb(null,forums) if uids?.length is 0
      projection = {nm:1,nm_zh:1,nm_en:1,roles:1}
      UserModel.getListByIds uids, {projection}, (err, users)->
        if err
          debug.error 'Forum find list user err', err
          return cb(err,null)
        return cb(null,forums) if users?.length is 0
        userKV = {}
        for user in users
          user.isAdmin = UserModel.accessAllowed('forumAdmin',user)
          userKV[user._id] = user
        for forum in forums
          # for checkServerStatus.sh
          if forum.mt
            forum.mtInSeconds = +(new Date(forum.mt).getTime() / 1000).toFixed(0)
          forum.author = userKV[forum.uid]
          getFullProvName forum
          # 处理多语言内容显示
          processMultilingualContent(forum, locale)
        handleDBRet err,forums,cb

  # [forum] getForumDetail
  @findDetail:({isedit,locale,id,type,gid,isProp,uid},cb)->
    if isProp
      # for prop detail page
      fields = {cc:1, del:1, discmnt:1}
    else
      fields = {vcapp:0,vcd:0, history:0}
      if 'summary' is type
        fields = {vc:1, cc:1, vt:1, fav:1, cmntl:1, realtorOnly:1,readStatus:1, tag:1 }
    q = {_id:id}
    q.uid = uid if uid
    Col = getCol gid
    Col.findOne q,{fields}, (err,ret)->
      getFullProvName ret
      if ret and (not isedit)
        # 处理多语言内容显示
        processMultilingualContent(ret, locale)
        # 处理评论的多语言显示
        if ret.cmnts
          processMultilingualComments(ret.cmnts, locale)
        if ret.thumb
          ret.thumb = replaceRM2REImagePathCheckDataFormat(ret.thumb)
        if ret.photos
          ret.photos = replaceRM2REImagePathCheckDataFormat(ret.photos)
      handleDBRet err,ret,cb

  # [forum] findPostByTp
  @findByTp:({tp,gid},cb)->
    Col = getCol gid
    Col.findOne {tp}, {fields:{ _id:1,tp:1}}, (err,_post)->
      getFullProvName _post
      handleDBRet err,_post,cb

  # [forum] adClick
  @incAdClick:({id,index,gid},cb)->
    inc={}
    inc['vcad' + index] = 1
    Col = getCol gid
    Col.updateOne {_id:id}, {$inc:inc}, {upsert:false}, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] edm
  @updateEdmStatus:({id,type,del,gid},cb)->
    edmts = new Date()
    # type 'edm' # edm whole page
    # type 'edmw' # weekly edm
    # type 'edmd' # daily edm

    if del
      cmd = {$unset:{}}
      cmd.$unset[type] = 1
    else
      cmd = {$set: {}}
      cmd.$set[type] = edmts
    Col = getCol gid
    Col.updateOne {_id:id}, cmd, {upsert:false}, (err,ret)->
      ret.edmts = edmts if ret
      handleDBRet err,ret,cb

  # [forum] changeStatus
  @findGroupForumReaders:(id,cb)->
    GroupForumCol.findOne {_id:id},{fields:{readers:1}},(err, forum)->
      handleDBRet err,forum,cb

  @findGroupForumReadersAsync =  functionHelper.addAsyncSupport Forum.findGroupForumReaders

  # [forum] changeStatus
  @changeGroupForumStatus:({id,user,forum,status},cb)->
    reader = forum?.readers?.find((_reader)->
      return _reader.uid.toString() is user._id.toString()
      )
    if reader
      query =   {_id:id,'readers.uid': user._id}
      update =  {$set: {'readers.$.status':status,'readers.$.mt': new Date()}}
    else
      query = {_id:id}
      update = {$push:{readers:{uid: user._id,ts: new Date(),nm:user.nm, status:status}}}
    GroupForumCol.updateOne query, update,{upsert:false},(err)->
      cb err

  # [forum] increase forum vc
  @incViewCount:({id,gid,post,devType,forumHotThreshold},cb)->
    Col = getCol gid
    inc = {vc:1, vcd:1}
    if devType is 'app'
      inc.vcapp = 1
    update = {$inc:inc}
    if (post.vc > forumHotThreshold) and (!post.tags or post.tags.indexOf('HOT') < 0)
      update.$addToSet = {tags:'HOT'}
    Col.updateOne {_id: id}, update,(err,ret)->
      handleDBRet err,ret,cb

  # [forum] findRelatedPosts
  @findRelated:({tpbl,tp,gid},cb)->
    if tpbl?.length
      query = {$or: [{tp:{$in:tpbl}}, {tpbl:{$in:tpbl}}]}
      query = {$or: [{tp:{$in:tpbl}}, {tpbl:{$in:tpbl}},{tpbl: tp}]} if tp
    else
      query = {tpbl: tp} if tp
    query.del = {$ne:true}
    Col = getCol gid
    Col.findToArray query, {fields:{ _id:1, tl:1, tp:1, tpbl: 1, thumb: 1}}, (err, ret) ->
      for item in ret
        item.thumb = replaceRM2REImagePathCheckDataFormat(item.thumb) if item.thumb
      handleDBRet err,ret,cb

  # [forum] topics
  @findAllTopics:({gid},cb)->
    Col = getCol gid
    Col.distinct 'tp', { 'tp' : { $nin : ['', null] } }, (err, data)->
      handleDBRet err,data,cb

  # [forum] recommend
  @findRecommend:({gid,similars},cb)->
    if similars?.length
      q = {_id:{$in:similars}}
      limit = 20
    else
      limit = 15
      q = {
        $or:[
          {
            'mt': {
              $gte: new Date(Date.now() - 3600000 * 24 * 14)
            },
            tags:'HOT'
          },
          {
            sticky:{$exists:true}
          }
        ]
      }
    sort = {ts:-1}
    projection = {fields: {_id:1,tl: 1,thumb: 1}, sort, limit}
    Col = getCol gid
    Col.findToArray q, projection, (err, ret) ->
      for item in ret
        item.thumb = replaceRM2REImagePathCheckDataFormat(item.thumb) if item.thumb
      handleDBRet err,ret,cb

  # [forum] savePost
  @saveOne:({post, gid, promptTemplates = []}, cb)->
    Col = getCol gid
    if post.thumb
      post.thumb = removeIRM2REImagePathCheckDataFormat(post.thumb)
    if post.photos
      post.photos = removeIRM2REImagePathCheckDataFormat(post.photos)
    
    # 新增：处理 draft 状态，使用 exp 字段替代 draft 字段
    if post.draft
      post.exp = new Date(Date.now() + DRAFT_EXPIRY_MS) # 365天后过期
      delete post.draft # 删除 draft 字段，只保留 exp
    
    if post._id
      postId = post._id  # 保存ID用于后续翻译
      delete post.
      update = {}
      update.$set = post
      update.$unset = {exp:1} if not post.draft
      Col.updateOne {_id:postId},update,(err, ret)->
        if err
          return handleDBRet err,ret,cb

        # 异步启动翻译（不阻塞响应）
        if promptTemplates?.length > 0 and (post.m or post.tl)
          process.nextTick ->
            try
              await Forum.translateForumPost {
                postId,
                gid,
                promptTemplates
              }
            catch error
              debug.error "异步翻译Forum帖子失败:", error

        handleDBRet err,ret,cb
    else
      Col.insertOne post,(err, ret)->
        if err
          return handleDBRet err,ret,cb

        # 异步启动翻译（不阻塞响应）
        if promptTemplates?.length > 0 and (post.m or post.tl)
          postId = ret?.ops?[0]?._id or ret.insertedId
          process.nextTick ->
            try
              await Forum.translateForumPost {
                postId,
                gid,
                promptTemplates
              }
            catch error
              debug.error "异步翻译Forum帖子失败:", error

        handleDBRet err,ret,cb

  # [forum] updatePost
  @updateOne:({post, history, gid, uid, promptTemplates = []}, cb)->
    id = post?._id
    tpbl = post?.tpbl
    post.uid = uid if uid
    Col = getCol gid

    # 1. 获取原始数据以检测内容变化（仅在需要翻译时）
    originalPost = null
    if promptTemplates?.length > 0 and (post.m or post.tl)
      originalPost = await Col.findOne {_id: id}, {fields: {m: 1, tl: 1}}

    # 2. 检测内容是否真的发生了变化
    contentChanged = false
    titleChanged = false
    if originalPost
      contentChanged = post.m? and (post.m isnt originalPost.m)
      titleChanged = post.tl? and (post.tl isnt originalPost.tl)

    # 3. 只有在内容真的改变时才需要翻译
    needsTranslation = promptTemplates?.length > 0 and (contentChanged or titleChanged)

    # 4. 处理字段删除逻辑（保持原有逻辑）
    for element in ['_id','cmnts','favs','sticky','vc','vcd','cc','del','history','ts']
    # do not reset ts
      delete post[element]
    for element in ['mt','cmt','vt']
    # fix: when mt changed to string, it won't show up on list any more.
      if 'string' is typeof post[element]
        post[element] = new Date(post[element])

    # 5. 过滤多语言字段，这些字段应该通过翻译逻辑单独处理
    multilingualFields = ['m_zh', 'm_zh_cn', 'm_en', 'm_kr', 'tl_zh', 'tl_zh_cn', 'tl_en', 'tl_kr']
    for field in multilingualFields
      delete post[field]

    # 6. 构建更新对象
    update = {}

    # 修复空对象处理问题：检查history是否有实际内容
    if history and typeof history is 'object' and Object.keys(history).length > 0
      update.$push = {history:history}

    unless post?.tp?.trim()
      update.$unset = {tp:1}
      delete post.tp
    unless post?.reno
      update.$unset = {reno:1}
      delete post.reno
    
    # 新增：处理 draft 和 exp 字段
    if post.draft
      # 设置为 draft 时，添加过期时间（365天）
      delete post.draft
      post.exp = new Date(Date.now() + DRAFT_EXPIRY_MS)
    else
      # publish 时删除 exp 字段
      update.$unset = update.$unset or {}
      update.$unset.exp = 1

    #for element in ['rank','adIntop','adInBtop','adInlist','showRequestInfo','city','prov','cnty','ncity']
    for element in ['rank','adInlist','showRequestInfo','city','prov','cnty','ncity']
      unless post[element]
        update.$unset ?= {}
        update.$unset[element] = 1
        delete post[element]

    for element in ['photos','thumb']
      if post[element]?.length
        post[element] = removeIRM2REImagePathCheckDataFormat(post[element])
      else
        update.$unset ?= {}
        update.$unset[element] = 1
        delete post[element]
    
    # 需要重新翻译时,去除原有的翻译
    update.$unset ?= {}
    for item in SUPPORTED_COMMENT_LANGUAGES
      update.$unset["m_#{item}"] =1 if contentChanged
      update.$unset["tl_#{item}"] = 1 if titleChanged


    update.$set = post

    # 7. 执行数据库更新
    try
      ret = await Col.updateOne {_id: id}, update, {upsert:true}
    catch err
      return handleDBRet err,ret,cb

    # 8. 异步启动翻译（仅在内容真的改变时）
    if needsTranslation
      process.nextTick ->
        try
          await Forum.translateForumPost {
            postId: id,
            gid,
            promptTemplates
          }
        catch error
          debug.error "异步翻译Forum帖子失败:", error

    handleDBRet err,ret,cb

  # [a_wordpressSync] wordPressEcho
  @updateFromWordpress:({
    site_url,
    adminUid,
    ID,
    post_date_gmt,
    post_modified_gmt,
    post_content,
    post_title,
    seo,
    categories,
    featured_image_url
  },cb)->
    post = {}
    post.wpHosts = [{wpid:ID, wpHost:site_url, ts: new Date(), mt: new Date(), status: 'wpNew'}]
    post.uid = adminUid
    post.ts = convertToGMT post_date_gmt
    post.mt = convertToGMT post_modified_gmt
    post.m = post_content if post_content
    post.tl = post_title if post_title
    post.meta_desc = seo?._yoast_wpseo_metadesc ||''
    post.meta_keywords = seo?._yoast_wpseo_focuskw ||''
    post.tags = getTagsByCategoryIds(site_url, categories)
    post.thumb = featured_image_url || ''
    update = {}
    update.$set = post
    query = {'wpHosts.wpid': ID, 'wpHosts.wpHost': site_url}
    ForumCol.updateOne query, update, {upsert:true}, (err,ret)->
      handleDBRet err,ret,cb

  # [a_wordpressSync] wordPressEcho
  @updateCommentFromWordpress:({
    comment_post_ID,
    comment_id,
    site_url,
    comment_content,
    comment_date_gmt,
    comment_author
  },cb)->
    cmnt = {}
    cmnt.wpHosts = "#{comment_id}@#{site_url}"
    cmnt.m = comment_content
    cmnt.ts = convertToGMT comment_date_gmt
    cmnt.fornm = comment_author
    update = {$push: {cmnts:cmnt}, $inc: {cc:1}}
    update.$set = {cmt: cmnt.ts}
    query = {'wpHosts.wpid': comment_post_ID, 'wpHosts.wpHost': site_url}
    ForumCol.updateOne query, update, {upsert:false}, (err,ret)->
      handleDBRet err,ret,cb

  # [a_wordpressSync] wordPressEcho
  @deleteFromWordpress:({id,host},cb)->
    query = {'wpHosts.wpid': id, 'wpHosts.wpHost': host}
    update = {$set:{del:'delete from wordpress'}}
    ForumCol.updateOne query, update, {upsert:false}, (err,ret)->
      handleDBRet err,ret,cb

  # [a_wordpressSync] wordPressEcho
  @deleteCommentFromWordpress:(id,cb)->
    query = {'cmnts.wpHosts': {$regex : id }}
    update ={$set:{'cmnts.$.del':'delete from wordpress'}}
    ForumCol.findOneAndUpdate query, update, {upsert:false}, (err,ret)->
      handleDBRet err,ret,cb

  # [comments] setReadersStaus
  @setReadersStaus:({gid,id},cb)->
    Col = getCol gid
    Col.updateOne {_id:id, 'readers.status':'read'},{$set:{'readers.$[].status':'toReview'}}, (err,ret)->
      return cb err if err
      return cb null

  # [forum] shared
  @incShared:({id,gid},cb)->
    update = {$inc:{'shr': 1, 'shrd': 1}}
    Col = getCol gid
    Col.updateOne {_id:id},update,{upsert:false,returnDocument:'after'},(err)->
      handleDBRet err,null,cb

  # [forum] updateMt
  @updateMt:({id,gid,mt},cb)->
    Col = getCol gid
    Col.updateOne {_id:id}, {$set:{mt}}, (err,ret)->
      handleDBRet err,ret,cb

  
  # [forum] pushNotifyUsers
  @updatePN:({postid,groupUnread,gid},cb)->
    Col = getCol gid
    update = if groupUnread then {pn_unread:1}  else {pn:1}
    Col.updateOne {_id: postid},{$set:update},(err,ret)->
      handleDBRet err,ret,cb
  @updatePNAsync =  functionHelper.addAsyncSupport Forum.updatePN

  # [forum] updateTopic
  @updateTopic:({oldtp,post,gid},cb)->
    Col = getCol gid
    Col.updateMany {tpbl:oldtp}, {$set:{'tpbl.$': post.tp}}, {multi:true}, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] delTopic
  @deleteTopic:({oldtp,gid},cb)->
    Col = getCol gid
    Col.updateMany {tpbl: oldtp}, {$pull:{tpbl: oldtp}}, {multi:true}, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] sticky
  @sticky:({type,gid,id,sticky,realtorOnly},cb)->
    param = {}
    if ('sticky' is type) and sticky
      param = {$set:{sticky:sticky, sticky_mt: new Date(), mt: new Date()}}
    else if ('sticky' is type)
      param = {$unset:{sticky:1, sticky_mt: 1}}
    else if ('realtorOnly' is type) and realtorOnly
      param = {$set:{realtorOnly:true}}
    else if ('realtorOnly' is type)
      param = {$unset:{realtorOnly:1}}
    Col = getCol gid
    Col.updateOne {_id: id}, param , (err,ret)->
      handleDBRet err,ret,cb

  # [forum] noCpm
  @setNoCPM:({id,noCpm,gid},cb)->
    if noCpm
      update={$set: {noCpm:true}}
    else
      update={$unset:{noCpm:1}}
    Col = getCol gid
    Col.updateOne {_id: id}, update, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] editTag
  @editTag:({newTag,tag,gid},cb)->
    Col = getCol gid
    Col.updateMany {tags:tag}, {$set:{'tags.$': newTag.key}}, {multi:true}, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] delTag
  @deleteTag:({tag,gid},cb)->
    #remove tags in all the post.
    Col = getCol 'gid'
    Col.updateMany {tags:tag}, {$pull:{tags:tag}}, {multi:true}, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] delPost
  @delete:({id,del,gid},cb)->
    set = {del:del, mt: new Date()}
    Col = getCol gid
    Col.updateOne {_id: id}, {$set:set}, (err,ret)->
      handleDBRet err,ret,cb

  # [forum] delPostFromDb
  @deleteFromDb:({id,gid},cb)->
    Col = getCol gid
    Col.deleteOne {_id: id}, (err,ret)->
      handleDBRet err,ret,cb

  # [comments]
  @favourite:({id,fav,user,gid},cb)->
    if fav
      update={$addToSet: {favs:user._id}}
    else
      update={$pull:{favs:user._id}}
    Col = getCol gid
    Col.updateOne {_id: id}, update, (err,ret)->
      handleDBRet err,ret,cb

  # [comments] 1.5/rating
  @updateUserRating:({id,rating,user,gid},cb)->
    set={}
    set['rating.$.r'] = rating
    query = {_id: id, 'rating.uid':user._id}
    Col = getCol gid
    Col.findOneAndUpdate query, {$set:set},{ returnDocument:'after'}, (err,ret)->
      return handleDBRet(err, null, cb) if err
      if ret.lastErrorObject.updatedExisting
        return handleDBRet(null,ret,cb)
      else
        push = {rating:{uid:user._id,r:rating}}
        Col.findOneAndUpdate {_id: id}, {$push:push},{returnDocument:'after'}, (err,ret)->
          return handleDBRet(err,null,cb) if err
          handleDBRet err,ret,cb

  @disableComment:({id,discmnt,gid},cb)->
    if discmnt
      param = {$set:{discmnt:discmnt, discmnt_mt: new Date()}}
    else
      param = {$unset:{discmnt:1, discmnt_mt:1}}
    Col = getCol gid
    Col.updateOne {_id: id}, param , (err,ret)->
      handleDBRet err,ret,cb

  # [comments] stickCmnt
  @stickyComment:({id,index,sticky,gid},cb)->
    set = {}
    set['cmnts.'+index+'.sticky'] = sticky
    set['cmnts.'+index+'.mt'] = new Date()
    set['cmnts.'+index+'.sticky_mt'] = new Date()
    set['cmt'] = new Date()
    Col = getCol gid
    Col.findOne {_id: id}, {fields: {m:0}},(err, ret) ->
      return handleDBRet(err,null,cb) if err
      return handleDBRet('no post found or reached comments limit',null,cb) unless ret
      if ret.src == 'property'
        if sticky
          set.cmntl = ret.cmnts[index].m.substr(0,20) unless ret.cmnts[index].del
        else
          resetCmntl ret, index, set, null, true
      Col.updateOne {_id: id}, {$set:set}, (err,ret)->
        ret.cmntl = set.cmntl
        handleDBRet err,ret,cb

  # [comments] incTupCounter
  @incTupCounter:({id,tup,index,Col},cb)->
    tupnum = if tup then 1 else -1
    inc ={}
    inc['cmnts.'+index+'.tupcnt']=tupnum
    Col.updateOne {_id: id}, {$inc:inc}, (err,ret)->
      handleDBRet err,ret,cb

  # [comments] tupCmnt
  @tupComment:({id, gid, tup, index, uid},cb)->
    set={}
    set['rating.$.'+index] = tup
    Col = getCol gid
    Col.updateOne {_id: id, 'rating.uid':uid}, {$set:set}, (err,ret)->
      return handleDBRet(err,null,cb) if err
      if ret.modifiedCount
        return Forum.incTupCounter {id, tup, index, Col},(err,ret)->
          return handleDBRet err,ret,cb
      else
        push = {rating:{uid:uid}}
        push.rating[index.toString()]=tup
        Col.updateOne {_id: id}, {$push:push}, (err,ret)->
          return handleDBRet(err,null,cb) if err
          Forum.incTupCounter {id, tup, index, Col}, (err,ret)->
            handleDBRet err,ret,cb

  # [comments] addCmnt
  @addComment:({id, cmnt, src, user, gid, userLanguage, translationTemplates},cb)->
    uid = user._id

    # 添加语言标识到评论对象
    cmnt.lang = userLanguage if userLanguage

    update = {$push:{cmnts:cmnt}, $inc:{cc:1}, $set:{cmt: new Date(), mt: new Date()}}
    update.$set.cmntl = cmnt.m.substr(0,20) if 'property' is src
    query = {_id: id, $or:[{cc:{$lt:1000}},{cc:{$exists:false}}]}
    Col = getCol gid
    Col.findOneAndUpdate query, update,{upsert:false,returnDocument:'after'}, (err,ret)->
      return cb(err,null) if err
      unless (post = ret?.value) and ret.lastErrorObject.n
        return cb('Add comments failed')
      len = post.cmnts?.length
      #can not add duplicate comment
      if (len >= 2) and (post.cmnts[len-1].m is post.cmnts[len-2].m) and (post.cmnts[len-1].uid?.toString() is post.cmnts[len-2].uid?.toString())
        Col.findOneAndUpdate {_id: id},{$pop:{cmnts: 1}, $inc:{cc: -1}},(err, ret) ->
          return cb(err,null) if err
          return cb('Duplicated comments not allowed',null)
      else # add success
        processCnmts post, user

        # 异步启动翻译（不阻塞响应）
        if translationTemplates?.length > 0 and cmnt.lang
          commentIndex = len - 1
          process.nextTick ->
            try
              await Forum.translateComment {
                id,
                commentIndex,
                sourceLanguage: cmnt.lang,
                translationTemplates,
                gid
              }
            catch error
              debug.error "异步翻译评论失败:", error

        return handleDBRet(null, post, cb)

  ###
  评论多语言翻译方法
  @param {String} id - 帖子ID
  @param {Number} commentIndex - 评论索引
  @param {String} sourceLanguage - 源语言代码
  @param {Array} translationTemplates - 翻译模板
  @param {String} gid - 群组ID（可选）
  @return {Promise<void>} 异步执行，无返回值
  ###
  @translateComment: ({id, commentIndex, sourceLanguage, translationTemplates, gid}) ->
    # 获取数据库集合
    Col = getCol gid

    # 查询当前评论
    post = await Col.findOne {_id: id}, {fields: {cmnts: 1}}


    unless post?.cmnts?[commentIndex]
      debug.error "评论不存在: postId=#{id}, commentIndex=#{commentIndex}"
      return

    comment = post.cmnts[commentIndex]
    originalContent = comment.m

    # 检查是否已有源语言标识，如果没有则添加
    unless comment.lang
      updateLangField = {}
      updateLangField["cmnts.#{commentIndex}.lang"] = sourceLanguage
      await Col.updateOne {_id: id}, {$set: updateLangField}

    # 确定需要翻译的语言（只翻译缺失的字段）
    missingLanguages = getMissingCommentLanguages(comment, sourceLanguage)

    if missingLanguages.length is 0
      debug.info "评论已有所有翻译版本，跳过翻译: postId=#{id}, commentIndex=#{commentIndex}"
      return

    debug.info "开始翻译评论: postId=#{id}, commentIndex=#{commentIndex}, 源语言=#{sourceLanguage}, 目标语言=[#{missingLanguages.join(', ')}]"

    # 执行翻译
    translations = await translateCommentToLanguages(
      originalContent,
      sourceLanguage.replace('-', '_'),
      missingLanguages,
      translationTemplates,
      {context: '用户评论内容',scenario: 'comment_translation'}
    )

    # 更新数据库
    if Object.keys(translations).length > 0
      updateFields = {}
      for lang, translatedText of translations
        updateFields["cmnts.#{commentIndex}.m_#{lang.replace('-', '_')}"] = translatedText

      await Col.updateOne {_id: id}, {$set: updateFields}

      debug.info "评论翻译完成: postId=#{id}, commentIndex=#{commentIndex}, 成功翻译语言=[#{Object.keys(translations).join(', ')}]"
    else
      debug.warn "评论翻译未产生结果: postId=#{id}, commentIndex=#{commentIndex}"

  ###
  Forum帖子多语言翻译方法
  @param {String} postId - 帖子ID
  @param {String} gid - 群组ID（可选）
  @param {Array} promptTemplates - 翻译模板
  @return {Promise<void>} 异步执行，无返回值
  ###
  @translateForumPost: ({postId, gid, promptTemplates}) ->
    # 获取数据库集合
    Col = getCol gid

    # 查询当前帖子
    post = await Col.findOne {_id: postId}

    unless post
      debug.error "帖子不存在: postId=#{postId}"
      return

    # 检测源语言（如果没有lang字段，尝试检测）
    sourceLanguage = post.lang
    unless sourceLanguage
      # 简单的语言检测逻辑，可以根据需要扩展
      if post.m or post.tl
        sourceLanguage = if (hasChinese(post.m) or hasChinese(post.tl)) then 'zh-cn' else 'en'
      else
        debug.warn "无法确定帖子源语言，跳过翻译: postId=#{postId}"
        return

    updateFields = {}
    hasTranslations = false

    # 翻译内容字段 (m)
    if post.m
      originalContent = post.m

      # 确定需要翻译的语言（只翻译缺失的字段）
      missingLanguages = []
      for lang in ['zh_cn', 'zh', 'en', 'kr']
        fieldName = "m_#{lang}"
        # 跳过源语言本身，只翻译缺失的目标语言
        displayLang = if lang is 'zh_cn' then 'zh-cn' else lang
        if (displayLang isnt sourceLanguage) and (not post[fieldName])
          missingLanguages.push displayLang

      if missingLanguages.length > 0
        debug.info "开始翻译帖子内容: postId=#{postId}, 源语言=#{sourceLanguage}, 目标语言=[#{missingLanguages.join(', ')}]"

        # 执行翻译
        translations = await translateCommentToLanguages(
          originalContent,
          sourceLanguage.replace('-', '_'),
          missingLanguages,
          promptTemplates,
          {context: 'forum_post',scenario: 'forum_translation'}
        )

        # 添加翻译结果到更新字段
        for lang, translatedText of translations
          fieldName = "m_#{lang.replace('-', '_')}"
          updateFields[fieldName] = translatedText
          hasTranslations = true

    # 翻译标题字段 (tl)
    if post.tl
      originalTitle = post.tl

      # 确定需要翻译的语言（只翻译缺失的字段）
      missingTitleLanguages = []
      for lang in ['zh_cn', 'zh', 'en', 'kr']
        fieldName = "tl_#{lang}"
        # 跳过源语言本身，只翻译缺失的目标语言
        displayLang = if lang is 'zh_cn' then 'zh-cn' else lang
        if (displayLang isnt sourceLanguage) and (not post[fieldName])
          missingTitleLanguages.push displayLang

      if missingTitleLanguages.length > 0
        debug.info "开始翻译帖子标题: postId=#{postId}, 源语言=#{sourceLanguage}, 目标语言=[#{missingTitleLanguages.join(', ')}]"

        # 执行翻译
        titleTranslations = await translateCommentToLanguages(
          originalTitle,
          sourceLanguage.replace('-', '_'),
          missingTitleLanguages,
          promptTemplates,
          {context: 'forum_post',scenario: 'forum_translation'}
        )

        # 添加翻译结果到更新字段
        for lang, translatedText of titleTranslations
          fieldName = "tl_#{lang.replace('-', '_')}"
          updateFields[fieldName] = translatedText
          hasTranslations = true

    # 添加源语言标识（如果没有的话）
    unless post.lang
      updateFields.lang = sourceLanguage

    # 更新数据库
    if hasTranslations or updateFields.lang
      await Col.updateOne {_id: postId}, {$set: updateFields}

      if hasTranslations
        translatedFields = Object.keys(updateFields).filter (key) -> key.startsWith('m_') or key.startsWith('tl_')
        debug.info "帖子翻译完成: postId=#{postId}, 成功翻译字段=[#{translatedFields.join(', ')}]"

      if updateFields.lang
        debug.info "帖子语言标识已添加: postId=#{postId}, lang=#{sourceLanguage}"
    else
      debug.info "帖子已有所有翻译版本，跳过翻译: postId=#{postId}"
    return

  # [comments] delCmnt
  @deleteComment:({id,del,index,gid},cb)->
    set = {}
    set['cmnts.'+index+'.del'] = del
    set['cmnts.'+index+'.mt'] = new Date()
    set['cmt'] = new Date()
    Col = getCol gid
    Col.findOne {_id: id}, {fields: {_id:1, cmnts:1,wpHosts: 1}},(err, post) ->
      return cb(MSG_STRINGS.DB_ERROR,null) if err
      return cb('no post found or reached comments limit',null) unless post
      if 'property' is post.src
        resetCmntl post, index, set, del
      inc = {cc:-1}
      inc = {cc: 1} if (false is del)
      Col.updateOne {_id:id}, {$set:set, $inc:inc}, (err,ret)->
        unless err
          ret.cmnts = post.cmnts
          ret.wpHosts = post.wpHosts
        handleDBRet err,ret,cb

  # [comments] delCmnt
  @handleCommentForWordpress:({id,index,del,cmnts,wpHosts,post_wpHosts,gid},cb)->
    if (false is del) and cmnts # rollback delete. get the rolled back comments.
      cmnt = cmnts[index]
      len = cmnts?.length
    if wpHosts and del
      deleteWpComment id, index, wpHosts
      update = {$unset:{}}
      update.$unset['cmnts.'+index+'.wpHosts'] =1
      Col = getCol gid
      Col.updateOne {_id: id}, update,(err, ret)->
        debug.critical err if err
    else if post_wpHosts?.length and cmnt
      # revert delete. add back to wordpress, only update when there is wphost.
      addWpComment id, cmnt, len-1, post_wpHosts

  # [forum] block
  @blockUser:({id,gid,user},cb)->
    update={$addToSet: {blkUids:user._id}}
    Col = getCol gid
    Col.updateOne {_id:id},update,(err,ret)->
      handleDBRet err,ret,cb

  # [sysAdmin] generalConfig
  @updateHotThreshold:({val},cb)->
    ForumCol.updateMany {vc: {$lt:val}}, {$pull:{tags:'HOT'}}, {multi:true},(err,ret)->
      return cb(err,null) if err
      ForumCol.updateMany {vc: {$gte:val}}, {$addToSet:{tags:'HOT'}}, {multi:true},(err,ret)->
        handleDBRet err,ret,cb

  # [index] fetchNeedReplayForum
  @findPropertyRelated:({city,dayDiff = 7},cb)->
    date = new Date().getTime() - (dayDiff*24*3600*1000)
    query =
      src: 'property'
      ts: {$gt:new Date(date)}
      cmnts: {$ne:null}
    query.city = city if city
    ForumCol.findToArray query,(err,ret)->
      for item in ret
        item.thumb = replaceRM2REImagePathCheckDataFormat(item.thumb) if item.thumb
        item.photos = replaceRM2REImagePathCheckDataFormat(item.photos) if item.photos
      handleDBRet err,ret,cb

  # [ForumUpdateVc] processPosts
  @findRecentForums:(cb)->
    q = { del:{$ne:true}, ts :{$gt: new Date(Date.now() - 40 * 3600 *1000) }}
    ForumCol.findToArray q,{fields:{vc:1,vcc:1,sticky:1,ts:1,mt:1,src:1}},(err, posts)->
      for item in posts
        item.thumb = replaceRM2REImagePathCheckDataFormat(item.thumb) if item.thumb
        item.photos = replaceRM2REImagePathCheckDataFormat(item.photos) if item.photos
      handleDBRet err,posts,cb

  # [ForumUpdateVc] processPost
  @updateVcc:(post,cb)->
    if vc = post.vc
      post.vcc ?= 1
      factor = 1
      if 'property' is post?.src
        factor = 0.35
      else if vc > 4500
        factor = 0
      else if vc > 3000
        factor = 0.25
      else if vc > 2000
        factor = 0.5
      else if vc > 1000
        factor = 0.75
      vccLimit = post.vc * factor
      vccInc = vccLimit - post.vcc
      return cb() if vccInc <= 0 # no need to increase
      if vccInc > 50
        vccInc = Math.random() * 60
      else
        vccInc = Math.random() * vccInc
      vcc = post.vcc + vccInc
    else
      vcc = 1
    set = $set:{vcc:Math.floor(vcc)}
    ForumCol.updateOne {_id:post._id}, set, (err,ret) ->
      handleDBRet err,ret,cb

  @findListForWholePage:({edmts,group,user},cb)->
    Col = ForumCol
    query = {'edm': {$gt:edmts}}
    if group
      query.gid = {$in:user.gids}
      Col = GroupForumCol
    query.lang = 'en' if user.locale is 'en';

    # forum with realtorOnly flag  only send to realtors
    if (not user.roles) or (user.roles.indexOf('realtor') < 0)
      query.realtorOnly = {$exists:false}

    debug.debug JSON.stringify query
    Col.findToArray query, {fields:{_id:1},sort:{ts:-1}, limit:20}, (err, ret) ->
      handleDBRet err, ret, cb

  @findListByIds:({group,ids,isWholePage},cb)->
    Col = if group then GroupForumCol else ForumCol
    fields = {gid:1,cc:1,uid:1,thumb:1,ts:1,mt:1,src:1,tags:1,favs:1,cmnts:1,tl:1,cmt:1}
    if isWholePage
      fields = {m:1,photos:1,ts:1,tl:1,fornm:1,avt:1,uid:1,src:1,gid:1}
    Col.findToArray {_id: {$in:ids}}, {fields}, (err, ret) ->
      for item in ret
        item.thumb = replaceRM2REImagePathCheckDataFormat(item.thumb) if item.thumb
        item.photos = replaceRM2REImagePathCheckDataFormat(item.photos) if item.photos
      handleDBRet err, ret,cb

  # @param type object
  # type arguments {
  #   edmts: date, # 1 week ago if weekly, 1 day ago if daily
  # }
  @findListForEdm:({avgs,forumlist,group,edmts,user,edmtsLogs},cb)->
    buildWeeklyForumQuery = (edmts)->
      # 周报新闻查询限制置顶，访问最多的内容，ts与mt均限制在一周内
      # old: tags:'HOT',
      query = $or: [
        {
          'edmw': {$gt:edmts},
        },
        {
          sticky: 'global',
          mt: {$gt:edmts},
          ts: {$gt:edmts},
        }
      ]
      query
    buildDailyForumQuery = ({user,forumlist,edmts})->
      query = $or: [
        {edmd:{$gt:edmts}}
        {$and:[
          {$or:[
            {'favs':user._id},
            {'cmnts.uid':user._id},
            {uid:user._id},
            {$and:[{_id:{$in:forumlist}, ts:{$gt: new Date(Date.now()  - 3600 * 24 * 1000 * 3)}}]}
          ]}
          {cmt:{$gt:edmts}}
          {del:{$ne:true}}
        ]}
      ]
      query
    
    if avgs.indexOf('weekly') >= 0
      forumEdmts = if group then new Date(edmtsLogs?.groupforumWeeklyEdmts) else new Date(edmtsLogs?.publicforumWeeklyEdmts)
      edmts =  if forumEdmts > edmts then forumEdmts else edmts
      query = buildWeeklyForumQuery edmts
    else
      forumEdmts = if group then new Date(edmtsLogs?.groupforumEdmts) else new Date(edmtsLogs?.publicforumEdmts)
      edmts =  if forumEdmts > edmts then forumEdmts else edmts
      query = buildDailyForumQuery {user,forumlist,edmts}

    query.gid = {$in:user.gids} if group
    if (not user.roles) or (user.roles.indexOf('realtor') < 0)
      query.realtorOnly = {$exists:false}

    debug.debug JSON.stringify query
    Col = if group then GroupForumCol else ForumCol
    Col.findToArray query, {fields:{_id:1}, sort: {vc:-1}, limit:20}, (err, ret) ->
      handleDBRet err,ret,cb

  ###
  # 获取所有草稿列表（包括公共论坛和群组论坛）
  # 使用相同的buildQuery逻辑，同时查询两个表并返回合并结果
  # 注意：此函数专门用于查找草稿，会查询两个表
  # @param {Object} params - 查询参数，与findList相同
  # @param {String} params.search - 论坛URL或论坛ID
  # @param {String} params.locale - 语言设置
  # @param {Object} params.user - 用户对象
  # @param {Boolean} params.isForumAdmin - 是否为论坛管理员
  # @param {Boolean} params.hasRoleRealtor - 是否具有房产经纪人角色
  # @param {String} params.src - 来源
  # @param {String} params.gid - 群组ID
  # @param {String} params.uid - 用户ID
  # @param {String} params.tag - 标签
  # @param {Array} params.sticky - 置顶状态
  # @param {String} params.reno - 房产编号
  # @param {Array} params.subcate - 子分类
  # @param {String} params.city - 城市
  # @param {String} params.prov - 省份
  # @param {String} params.cnty - 县区
  # @param {String} params.category - 分类
  # @param {String} params.yellowpage - 黄页
  # @param {String} params.author - 作者
  # @param {Array} params.gids - 群组ID列表
  # @param {Boolean} params.isGroupForum - 是否为群组论坛
  # @returns {Promise<Array>} 合并后的草稿列表
  ###
  @findAllDraftList: (params) ->
    # 构建查询条件
    query = buildQuery params
    
    # 设置字段投影
    fields = Object.assign({}, @LIST_FIELDS)

    # 设置排序规则
    sort = { 'rank': -1, 'sticky': -1, 'mt': -1 }
    
    # 查询群组论坛草稿
    # 为群组论坛调整排序规则
    groupSort = {'gid': -1, 'sticky': -1, 'mt': -1}
    groupProjection = {fields, sort: groupSort}
    groupForums = await GroupForumCol.findToArray query, groupProjection
    
    # 查询公共论坛草稿
    publicProjection = {fields, sort}
    publicForums = await ForumCol.findToArray query, publicProjection
    
    # 合并结果
    allForums = publicForums.concat(groupForums)
    
    # 如果没有数据，直接返回
    if allForums.length is 0
      return []
    
    # 处理语言转换和图片路径
    for forum in allForums
      if params.locale is 'zh'
        forum.tl = JT2FT forum.tl
      if params.locale is 'zh-cn'
        forum.tl = FT2JT forum.tl
      
      if forum.thumb
        forum.thumb = replaceRM2REImagePathCheckDataFormat(forum.thumb)
    
    # 收集用户ID
    uids = []
    for forum in allForums
      uid = forum.uid?.toString()
      if uid and (uids.indexOf(uid) is -1)
        uids.push(uid)
    
    # 如果没有用户ID，直接返回结果
    if uids.length is 0
      return allForums
    
    # 获取用户信息
    projection = {nm: 1, nm_zh: 1, nm_en: 1, roles: 1}
    users = await UserModel.getListByIdsAsync uids, {projection}
    
    if users?.length > 0
      # 构建用户键值对
      userKV = {}
      for user in users
        user.isAdmin = UserModel.accessAllowed('forumAdmin', user)
        userKV[user._id] = user
      
      # 为论坛添加作者信息和省份信息
      for forum in allForums
        if forum.mt
          forum.mtInSeconds = +(new Date(forum.mt).getTime() / 1000).toFixed(0)
        forum.author = userKV[forum.uid]
        getFullProvName forum
    
    # 返回结果
    return allForums

  ###
  # Determines if a post is a personal post in a .cn domain.
  # Returns false if the post's author is a forum admin.
  # @param {String} host - The domain host string
  # @param {Object} post - The post object, must contain uid and fornm
  # @returns {Promise<Boolean>} True if .cn domain and personal post, false otherwise
  ###
  @isPersonalPostInCnDomain: (host, post) ->
    if not host or not post
      return false
    if post.uid
      try
        user = await UserModel.findByIdAsync(post.uid,{projection:{roles:1}})
        if user and UserModel.accessAllowed('forumAdmin', user)
          return false
      catch err
        debug.error 'isPersonalPostInCnDomain user lookup error:', err
    return host.indexOf('.cn') > -1 and post.fornm

  ###
  # 获取推送目标用户列表
  # 整合了gid、dev、realtorOnly的处理逻辑和用户筛选逻辑，与原pushNotifyUsers逻辑保持一致
  # @param {Object} targetUser - 目标用户配置对象
  # @param {String} targetUser.gid - 群组ID
  # @param {Array} targetUser.uids - 用户ID列表（群组成员）
  # @param {Boolean} targetUser.realtorOnly - 是否只推送给房产经纪人
  # @param {Boolean} targetUser.groupUnread - 是否只推送给群组未读用户
  # @param {Boolean} targetUser.isDev - 是否为开发模式
  # @param {String} lang - 语言设置
  # @returns {Promise<Array>} 用户信息列表
  ###
  @getTargetUserListPn: (targetUser, lang = 'zh') ->
    return Promise.reject('Missing targetUser') unless targetUser
    
    # 构建查询参数，遵循原pushNotifyUsers的逻辑
    msgParams = {swch: 'pnNoNews'}
    
    # 1. 处理群组推送逻辑
    if targetUser.gid and targetUser.uids?.length
      if targetUser.groupUnread
        # 群组未读推送：过滤掉已读用户
        try
          result = await @findGroupForumReadersAsync targetUser.gid
          readUsers = result?.readers?.filter((reader) -> reader.status is 'read')
          if readUsers?.length
            readuids = readUsers.map((user) -> user.uid.toString())
            filteredUids = targetUser.uids.filter((id) -> 
              readuids.indexOf(id.toString()) < 0
            )
            msgParams.q = {_id: {$in: filteredUids}}
          else
            msgParams.q = {_id: {$in: targetUser.uids}}
        catch err
          debug.error 'getTargetUserListPn findGroupForumReaders error:', err
          msgParams.q = {_id: {$in: targetUser.uids}}
      else
        # 普通群组推送：直接使用群组成员ID列表
        msgParams.q = {_id: {$in: targetUser.uids}}
    else
      # 全局推送：基础查询条件（与原逻辑一致）
      msgParams.q = {
        $or: [
          # BUG: 这个是or条件，如果用户local是en，但是splang为Mandarin，则会发送推送，但是空内容
          {locale: {$ne: 'en'}}, 
          {splang: 'Mandarin'}, 
          {splang: 'Cantonese'}
        ]
      }
      
      # 2. 处理开发模式和房产经纪人逻辑（与原逻辑一致）
      if targetUser.isDev
        msgParams.q.roles = '_dev'
        if targetUser.realtorOnly
          msgParams.q.roles = {$all: ['_dev', 'realtor']}
      else
        if targetUser.realtorOnly
          msgParams.roles = 'realtor'  # 注意：这里是msgParams.roles，不是msgParams.q.roles
        else
          msgParams.t = 'all'  # 注意：这里是msgParams.t，不是msgParams.q.t
    
    # 3. 获取注册用户
    users = []
    # for i in [0..1000]
    #   users.push {_id:'56103d1c3a941ed7662ea398',pn:"ios:#{i}7d18fbf49a520530586bc073e8d54f1786a87fe89a5ce4e4d8ecc7711f8fbc62",locale:'zh',pnBadge:i}
    try
      users = await UserModel.getPushNotifyUsers msgParams
    catch err
      debug.error 'getTargetUserListPn getPushNotifyUsers error:', err
      throw err
    
    # 4. 处理未注册用户的pnToken（仅全局推送且启用时）
    if not targetUser.gid and not targetUser.isDev
      try
        pnTokens = await PnToken.findToArray {}, {projection:{pnBadge:1},sort: {ts: -1}, limit: 100000}
        
        # 优化方案1：使用map转换后concat合并（推荐）
        # 一次性转换所有pnToken，避免逐个push操作
        tokenUsers = pnTokens.map (pnToken) ->
          {
            _id: pnToken._id
            pn: pnToken._id
            locale: 'zh-cn' # 默认中文
            pnBadge: pnToken.pnBadge or 0
          }
        users = users.concat(tokenUsers)
      catch err
        debug.error 'getTargetUserListPn pnToken error:', err
        # pnToken获取失败不影响注册用户推送
    
    return users

MODEL 'Forum',Forum
