debug = DEBUG()
ProcessStatusCol =  COLLECTION('vow','processStatus')
ProcessStatusRniCol =  COLLECTION('rni','processStatus')
DB_RNI = 'rni'
{
  updateProcessState,
  RUNNING_NORMAL,
  NORMAL_STATUS_LIST,
  WARNING_STATUS_LIST,
  ERROR_STATUS_LIST,
  ALL_STATUS_LIST,
  FINISH_WITHOUT_ERROR
} = INCLUDE('libapp.processHelper')
###
  for batch that run 24/7 (eg. watch)
    normal: batch is running as normal
    exit: exit by error
  for batch that run many times (eg. import start)
    running: set to running when batch start
    complete: when finished
    error: exit by error
###

NORMAL = 'normal'
WARNING= 'warning'
ERROR = 'error'
ONE_MINUTES = 1000*60
TEN_MINUTES = ONE_MINUTES*10
ONE_DAY = 24*60*ONE_MINUTES
# THREE_DAYS = 3*ONE_DAY
# gSessionExpireCount = 0
###
Spctial cases:
  normal:

  warning:
    1. nextTs is greater than now (in normal)
    2. _mt + [duration] is passed for [duration] minutes (in running)
  error:
  
###
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  ProcessStatusCol.createIndex {filePath:1}


class ProcessStatus
  ###
  @param {object} object
  @param {string} object.prevStatus - status of last time,change this when status is changed.
  @param {string} object.status - should in NORMAL_STATUS_LIST, WARNING_STATUS_LIST or ERROR_STATUS_LIST
  @param {string} object.filePath
  @param {Date} object.nextTs
  @param {Date} object._mt - modified time, generated by mongo driver
  @param {number} object.duration - estimated finish time in minute
  @param {string} object.notify - error message form batch, result.status will be WARNING or ERROR if notify exist
  @param {string} dbName - optional database tag; if 'rni', returned name value will be suffixed with _rni
  @return {objet} result
  @return {string} result.name
  @return {string} result.status - NORMAL | WARNING | ERROR
  @return {string} result.message - "[status]: [batch name], [message]."
  ###
  @handleStatus:({
    prevStatus,
    status,
    _id,
    nextTs,
    _mt,
    duration, # NOTE: no duration in db for now
    notify}, dbName='')->
      # Check inputs
      name = _id
      # choose collection by db tag
      collection = if (dbName?.toLowerCase?() is DB_RNI) then ProcessStatusRniCol else ProcessStatusCol
      isRni = dbName?.toLowerCase?() is DB_RNI
      nameOut = if (name and isRni) then "#{name}_#{DB_RNI}" else name
      # helper to build static-key result; omit name when missing
      formatResult = (statusValue, messageValue)->
        if nameOut
          return {name: nameOut, status: statusValue, message: messageValue}
        else
          return {status: statusValue, message: messageValue}
      duration = 0 if (not duration) or (typeof duration isnt 'number')
      if not name
        return formatResult(WARNING, "#{WARNING}: File name is empty.")
      if not status
        return formatResult(WARNING, "#{WARNING}: #{name}, No status.")
      if prevStatus isnt status
        if (prevStatus isnt FINISH_WITHOUT_ERROR) and\
        (status isnt FINISH_WITHOUT_ERROR)
          notify ?=''
          notify += "Status Change, prev:#{prevStatus}, current:#{status}"
          #update DB,change prevStatus to current status
        
        collection.updateOne {_id},{$set:{prevStatus:status}},(err,ret)->
          debug.error err if err

      # Return error and warning
      if status in ERROR_STATUS_LIST
        notify = notify or 'Status is in error'
        return formatResult(ERROR, "#{status}: #{name}, #{notify}")
      if status in WARNING_STATUS_LIST
        notify = notify or 'Status is in warning'
        return formatResult(WARNING, "#{status}: #{name}, #{notify}")
      # Handling unsupported status \
      #(not ERROR_STATUS_LIST, not WARNING_STATUS_LIST, not NORMAL_STATUS_LIST)
      if not (status in NORMAL_STATUS_LIST)
        notify = notify or 'Unsupported status.'
        return formatResult(WARNING, "#{WARNING}: #{name}, #{notify}")
      
      # Handling normal status but may has problem

      # Handling estimation, statue === running
      isFrozen = false
      if (_mt) and (status is RUNNING_NORMAL)
        # duration should be updated by now, db.duration is est. end time, this value need to be updated when finish
        if duration and (Date.now() > (Date.parse(_mt) + (duration * ONE_MINUTES)))
          isFrozen = true
        if (Date.now() - Date.parse(_mt)) > ONE_DAY
          if not /weekly/i.test name
            isFrozen = true
      if isFrozen
        if duration
          return formatResult(WARNING, "#{WARNING}: #{name}: \
              Batch has running for more than #{duration} minutes.")
        else
          inactiveDays = (Date.now() - Date.parse(_mt))/ONE_DAY
          return formatResult(WARNING, "#{WARNING}: #{name}: \
              Batch has no update for more than #{inactiveDays} days.")

      # handle message, set status to warning
      if notify
        return formatResult(WARNING,  "#{NORMAL}: #{name}: #{notify}.")
      
      # Handling nextTs, set status to normal if no nextTs
      if not nextTs
        return formatResult(NORMAL, "#{NORMAL}: #{name}: No nextTs.")
      
      # passed nextTs for 10 minutes + duration
      tolerateNextTs = Date.parse(nextTs) + \
        TEN_MINUTES + (duration * ONE_MINUTES)
      # NOTE: 偶尔收到大量 Next ts is passed.
      if Date.now() > tolerateNextTs
        serverTime = new Date()
        nextTime = new Date(tolerateNextTs)
        return formatResult(WARNING, "#{WARNING}: #{name}: Next ts is passed. serverTime:#{serverTime}, nextTime:#{nextTime}")
      
      # normal
      return formatResult(NORMAL,  "#{NORMAL}: #{name}: Normal.")
    
  ###
  @param {array} files - an array of batch name
  @return {array} - an array of handleStatus result

  when status change, save current status as previous status. send both status in email.
  ###
  @checkStatus:()->
    self = @
    try
      status = await ProcessStatusCol.findToArray {}
      formatedStatus1 = status.map (s)-> self.handleStatus(s)
      status = await ProcessStatusRniCol.findToArray {}
      formatedStatus2 = status.map (s)-> self.handleStatus(s, DB_RNI)
      #TODO: Separate the processStatus module
      return formatedStatus1.concat(formatedStatus2)
    catch error
      debug.error 'ProcessStatus.checkStatus', error
      throw new Error('DB error')



MODEL 'ProcessStatus',ProcessStatus
