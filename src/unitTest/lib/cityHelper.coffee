should = require 'should'
cityHelper = require '../../lib/cityHelper'

describe 'cityHelper', ->

  describe 'isSameCityOrSubCity', ->

    tests = [
      # 基本功能测试
      {
        description: '城市名称完全一致时应该返回true',
        city1: 'Toronto',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '城市名称大小写不同但内容一致时应该返回true',
        city1: 'toronto',
        city2: 'TORONTO',
        expected: true
      },
      {
        description: '城市名称有前后空格时应该返回true',
        city1: ' Toronto ',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '完全不同的城市名称时应该返回false',
        city1: 'Toronto',
        city2: 'Vancouver',
        expected: false
      },

      # 参数验证测试
      {
        description: '第一个参数为空字符串时应该返回false',
        city1: '',
        city2: 'Toronto',
        expected: false
      },
      {
        description: '第二个参数为空字符串时应该返回false',
        city1: 'Toronto',
        city2: '',
        expected: false
      },
      {
        description: '两个参数都为空字符串时应该返回false',
        city1: '',
        city2: '',
        expected: false
      },
      {
        description: '第一个参数为null时应该返回false',
        city1: null,
        city2: 'Toronto',
        expected: false
      },
      {
        description: '第二个参数为null时应该返回false',
        city1: 'Toronto',
        city2: null,
        expected: false
      },
      {
        description: '第一个参数为undefined时应该返回false',
        city1: undefined,
        city2: 'Toronto',
        expected: false
      },
      {
        description: '数字类型输入应该被正确处理',
        city1: 123,
        city2: '123',
        expected: true
      },
      {
        description: '相同数字类型输入应该返回true',
        city1: 123,
        city2: 123,
        expected: true
      },

      # 相似度测试
      {
        description: '轻微拼写错误(1个字符差异)应该返回true',
        city1: 'Toranto',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '轻微拼写错误(字符位置错误)应该返回false',
        city1: 'Toronot',
        city2: 'Toronto',
        expected: false
      },
      {
        description: '较大拼写错误(2个字符差异，超过10%)应该返回false',
        city1: 'Toranot',
        city2: 'Toronto',
        expected: false
      },
      {
        description: 'Hamilton轻微拼写错误应该返回true',
        city1: 'Hamiltn',
        city2: 'Hamilton',
        expected: true
      },

      # 子城市关系测试
      {
        description: 'Scarborough应该被识别为Toronto的子城市',
        city1: 'Scarborough',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为Scarborough的主城市',
        city1: 'Toronto',
        city2: 'Scarborough',
        expected: true
      },
      {
        description: 'North York应该被识别为Toronto的子城市',
        city1: 'North York',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为North York的主城市',
        city1: 'Toronto',
        city2: 'North York',
        expected: true
      },
      {
        description: 'Etobicoke应该被识别为Toronto的子城市',
        city1: 'Etobicoke',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为Etobicoke的主城市',
        city1: 'Toronto',
        city2: 'Etobicoke',
        expected: true
      },
      {
        description: 'East York应该被识别为Toronto的子城市',
        city1: 'East York',
        city2: 'Toronto',
        expected: true
      },
      {
        description: 'Toronto应该被识别为East York的主城市',
        city1: 'Toronto',
        city2: 'East York',
        expected: true
      },
      {
        description: 'Ancaster应该被识别为Hamilton的子城市',
        city1: 'Ancaster',
        city2: 'Hamilton',
        expected: true
      },
      {
        description: 'Hamilton应该被识别为Dundas的主城市',
        city1: 'Hamilton',
        city2: 'Dundas',
        expected: true
      },
      {
        description: 'Stoney Creek应该被识别为Hamilton的子城市',
        city1: 'Stoney Creek',
        city2: 'Hamilton',
        expected: true
      },

      # 子城市拼写错误容错测试
      {
        description: '子城市与主城市拼写错误时应该返回true',
        city1: 'Scarborough',
        city2: 'Toranto',
        expected: true
      },
      {
        description: '主城市拼写错误与子城市时应该返回false',
        city1: 'Toronot',
        city2: 'North York',
        expected: false
      },
      {
        description: '子城市拼写错误与主城市时应该返回true',
        city1: 'Scarborogh',
        city2: 'Toronto',
        expected: true
      },
      {
        description: '主城市与子城市拼写错误时应该返回true',
        city1: 'Toronto',
        city2: 'Noth York',
        expected: true
      },
      {
        description: 'Hamilton子城市拼写错误时应该返回true',
        city1: 'Ancastr',
        city2: 'Hamilton',
        expected: true
      },

      # 不相关城市测试
      {
        description: '不相关的城市应该返回false - Scarborough vs Vancouver',
        city1: 'Scarborough',
        city2: 'Vancouver',
        expected: false
      },
      {
        description: '不相关的城市应该返回false - Hamilton vs Calgary',
        city1: 'Hamilton',
        city2: 'Calgary',
        expected: false
      },
      {
        description: '不相关的城市应该返回false - Toronto vs Montreal',
        city1: 'Toronto',
        city2: 'Montreal',
        expected: false
      },
      {
        description: '子城市与不相关城市应该返回false',
        city1: 'North York',
        city2: 'Vancouver',
        expected: false
      },

      # 检查两个子城市
      {
        description: 'Etobicoke,North York应该被识别为Toronto的子城市',
        city1: 'Etobicoke',
        city2: 'North York',
        expected: true
      },
      {
        description: 'Ancaster应该被识别为Hamilton的子城市,London East应该被识别为London的子城市',
        city1: 'Ancaster',
        city2: 'London East',
        expected: false
      }
    ]

    tests.forEach (test) ->
      it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
        result = cityHelper.isSameCityOrSubCity(test.city1, test.city2)
        result.should.equal(test.expected)
        done()

  describe 'Custom similarity threshold test', ->
    # 自定义相似度阈值测试
    customThresholdTests = [
      {
        description: '使用严格阈值(5%)时轻微拼写错误应该返回true',
        city1: 'Toranto',
        city2: 'Toronto',
        threshold: 0.05,
        expected: true
      },
      {
        description: '使用宽松阈值(20%)时较大拼写错误应该返回false',
        city1: 'Toranot',
        city2: 'Toronto',
        threshold: 0.2,
        expected: false
      },
      {
        description: '使用严格阈值(5%)时Hamilton拼写错误应该返回true',
        city1: 'Hamiltn',
        city2: 'Hamilton',
        threshold: 0.05,
        expected: true
      },
      {
        description: '使用适中阈值(15%)时Hamilton拼写错误应该返回true',
        city1: 'Hamiltn',
        city2: 'Hamilton',
        threshold: 0.15,
        expected: true
      }
    ]

    customThresholdTests.forEach (test) ->
      testName = "#{test.description} - #{test.city1} vs #{test.city2}"
      it "#{testName} (threshold: #{test.threshold})", (done) ->
        result = cityHelper.isSameCityOrSubCity(test.city1, test.city2, test.threshold)
        result.should.equal(test.expected)
        done()

  describe 'isSimilarStrByLD', ->

    describe 'Basic functionality', ->
      basicTests = [
        # 基本功能测试
        {
          description: '完全相同的城市名应该返回true',
          city1: 'Toronto',
          city2: 'Toronto',
          expected: true
        },
        {
          description: '大小写不同但内容相同应该返回true',
          city1: 'toronto',
          city2: 'TORONTO',
          expected: true
        },
        {
          description: '前后空格应该被正确处理',
          city1: ' Toronto ',
          city2: 'Toronto',
          expected: true
        },
        {
          description: '多个空格应该被合并',
          city1: 'New  York',
          city2: 'New York',
          expected: true
        },
        {
          description: '完全不同的城市名应该返回false',
          city1: 'Toronto',
          city2: 'Vancouver',
          expected: false
        }
      ]

      basicTests.forEach (test) ->
        it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Parameter validation', ->
      paramTests = [
        {
          description: '第一个参数为空字符串应该返回false',
          city1: '',
          city2: 'Toronto',
          expected: false
        },
        {
          description: '第二个参数为空字符串应该返回false',
          city1: 'Toronto',
          city2: '',
          expected: false
        },
        {
          description: '两个参数都为空字符串应该返回false',
          city1: '',
          city2: '',
          expected: false
        },
        {
          description: '第一个参数为null应该返回false',
          city1: null,
          city2: 'Toronto',
          expected: false
        },
        {
          description: '第二个参数为null应该返回false',
          city1: 'Toronto',
          city2: null,
          expected: false
        },
        {
          description: '第一个参数为undefined应该返回false',
          city1: undefined,
          city2: 'Toronto',
          expected: false
        },
        {
          description: '数字类型应该被转换为字符串处理',
          city1: 123,
          city2: '123',
          expected: true
        }
      ]

      paramTests.forEach (test) ->
        it "#{test.description}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Word count difference logic', ->
      wordCountTests = [
        {
          description: '单词数相同且完全匹配应该返回true',
          city1: 'New York',
          city2: 'New York',
          expected: true
        },
        {
          description: '单词数相同但内容不同应该根据相似度判断',
          city1: 'New York',
          city2: 'Old York',
          expected: false  # 两个不同的词：New/Old，但编辑距离太大
        },
        {
          description: 'test York vs New York',
          city1: 'York',
          city2: 'New York',
          expected: false  # 只有一个词不同
        },
        {
          description: '单词数差异超过1应该返回false',
          city1: 'York',
          city2: 'New York City',
          expected: false
        },
        {
          description: '多个单词完全不同应该返回false',
          city1: 'New York',
          city2: 'Los Angeles',
          expected: false
        }
      ]

      wordCountTests.forEach (test) ->
        it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Similarity calculation with edit distance', ->
      similarityTests = [
        {
          description: '轻微拼写错误(1个字符差异)应该返回true',
          city1: 'Toranto',
          city2: 'Toronto',
          expected: true
        },
        {
          description: '轻微拼写错误(字符替换)应该返回true',
          city1: 'Hamilten',
          city2: 'Hamilton',
          expected: true
        },
        {
          description: '字符缺失应该返回true',
          city1: 'Toront',
          city2: 'Toronto',
          expected: true
        },
        {
          description: '字符多余应该返回true',
          city1: 'Torontoo',
          city2: 'Toronto',
          expected: true
        },
        {
          description: '较大拼写错误(超过10%阈值)应该返回false',
          city1: 'Toranot',
          city2: 'Toronto',
          expected: false
        },
        {
          description: '多词城市名中一个词的轻微错误应该返回true',
          city1: 'New Yrok',
          city2: 'New York',
          expected: false  # 两个不同的词：Yrok/York，编辑距离为2，超过阈值
        },
        {
          description: '多词城市名中一个词的较大错误应该返回false',
          city1: 'New Yxrk',
          city2: 'New York',
          expected: true  # 两个不同的词：Yxrk/York，编辑距离为2，在默认10%阈值内（8字符*10%=0.8，向上取整为1，但实际计算可能不同）
        }
      ]

      similarityTests.forEach (test) ->
        it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Custom allowDiffRatio parameter', ->
      customRatioTests = [
        {
          description: 'York vs New York',
          city1: 'York',
          city2: 'New York',
          expected: false
        },
        {
          description: '严格阈值(5%)下轻微错误应该返回true',
          city1: 'Toranto',
          city2: 'Toronto',
          allowDiffRatio: 0.05,
          expected: true
        },
        {
          description: '严格阈值(5%)下较大错误应该返回false',
          city1: 'Toranot',
          city2: 'Toronto',
          allowDiffRatio: 0.05,
          expected: false
        },
        {
          description: '宽松阈值(20%)下较大错误应该返回true',
          city1: 'Toranot',
          city2: 'Toronto',
          allowDiffRatio: 0.2,
          expected: false  # 两个不同的词，编辑距离为3，即使20%阈值也不够
        },
        {
          description: '宽松阈值(30%)下更大错误应该返回true',
          city1: 'Txrxntx',
          city2: 'Toronto',
          allowDiffRatio: 0.3,
          expected: true
        },
        {
          description: '即使宽松阈值下完全不同的词应该返回false',
          city1: 'Vancouver',
          city2: 'Toronto',
          allowDiffRatio: 0.5,
          expected: false
        }
      ]

      customRatioTests.forEach (test) ->
        testName = "#{test.description} - #{test.city1} vs #{test.city2}"
        it "#{testName} (ratio: #{test.allowDiffRatio})", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2, test.allowDiffRatio)
          result.should.equal(test.expected)
          done()

    describe 'Edge cases and boundary conditions', ->
      edgeCaseTests = [
        {
          description: '极短城市名(1个字符)完全匹配应该返回true',
          city1: 'A',
          city2: 'A',
          expected: true
        },
        {
          description: '极短城市名(1个字符)不匹配应该返回false',
          city1: 'A',
          city2: 'B',
          expected: true  # 两个不同的词：A/B，编辑距离为1，在默认10%阈值内（1字符*10%=0.1，向上取整为1）
        },
        {
          description: '极短城市名与长城市名应该返回false',
          city1: 'A',
          city2: 'Toronto',
          expected: false
        },
        {
          description: '单字符差异在短城市名中应该根据比例判断',
          city1: 'AB',
          city2: 'AC',
          expected: true  # 两个不同的词：AB/AC，编辑距离为1，在阈值内（2字符*10%=0.2，向上取整为1）
        },
        {
          description: '长城市名的轻微差异应该返回true',
          city1: 'Mississauga',
          city2: 'Mississauga',
          expected: true
        },
        {
          description: '长城市名的单字符差异应该返回true',
          city1: 'Mississauga',
          city2: 'Mississaaga',
          expected: true
        },
        {
          description: '数字城市名应该被正确处理',
          city1: '123',
          city2: '124',
          expected: true  # 1个字符差异，在阈值内
        },
        {
          description: '混合字母数字城市名应该被正确处理',
          city1: 'City123',
          city2: 'City124',
          expected: true
        }
      ]

      edgeCaseTests.forEach (test) ->
        it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Complex multi-word scenarios', ->
      multiWordTests = [
        {
          description: '三个词的城市名完全匹配应该返回true',
          city1: 'Niagara On Lake',
          city2: 'Niagara On Lake',
          expected: true
        },
        {
          description: '三个词中一个词不同应该返回true',
          city1: 'Niagara On Lake',
          city2: 'Niagara On River',
          expected: false  # 两个不同的词：Lake/River，编辑距离为5，超过阈值
        },
        {
          description: '三个词中两个词不同但相似应该返回true',
          city1: 'Niagara On Lake',
          city2: 'Niagra On Lke',
          expected: false  # 四个不同的词：Niagara/Niagra, Lake/Lke，超过2个不同词的限制
        },
        {
          description: '三个词中两个词完全不同应该返回false',
          city1: 'Niagara On Lake',
          city2: 'Toronto In City',
          expected: false
        },
        {
          description: '词序不同应该返回false',
          city1: 'New York City',
          city2: 'York New City',
          expected: true  # 词序不同，但只有2个不同的词：New/York，编辑距离在阈值内
        },
        {
          description: '部分词匹配应该根据差异数量判断',
          city1: 'Saint John City',
          city2: 'Saint John Town',
          expected: false  # 两个不同的词：City/Town，编辑距离为4，超过阈值
        }
      ]

      multiWordTests.forEach (test) ->
        it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Real-world city name scenarios', ->
      realWorldTests = [
        {
          description: '常见拼写错误 - Toronto',
          city1: 'Toranto',
          city2: 'Toronto',
          expected: true
        },
        {
          description: '常见拼写错误 - Hamilton',
          city1: 'Hamiltn',
          city2: 'Hamilton',
          expected: true
        },
        {
          description: '常见拼写错误 - Mississauga',
          city1: 'Mississaga',
          city2: 'Mississauga',
          expected: true
        },
        {
          description: '常见拼写错误 - Brampton',
          city1: 'Bramton',
          city2: 'Brampton',
          expected: true
        },
        {
          description: '带连字符的城市名完全匹配',
          city1: 'Whitchurch-Stouffville',
          city2: 'Whitchurch-Stouffville',
          expected: true
        },
        {
          description: '带连字符的城市名拼写错误',
          city1: 'Whitchurch-Stoufville',
          city2: 'Whitchurch-Stouffville',
          expected: true
        },
        {
          description: '带点号的城市名',
          city1: 'St. Catharines',
          city2: 'St. Catharines',
          expected: true
        },
        {
          description: '带点号的城市名拼写错误',
          city1: 'St. Catharins',
          city2: 'St. Catharines',
          expected: true
        },
        {
          description: '法语城市名',
          city1: 'Québec',
          city2: 'Quebec',
          expected: true  # 两个不同的词：Québec/Quebec，编辑距离为1，在阈值内
        },
        {
          description: '大小写混合的复杂城市名',
          city1: 'North York',
          city2: 'NORTH YORK',
          expected: true
        }
      ]

      realWorldTests.forEach (test) ->
        it "#{test.description} - #{test.city1} vs #{test.city2}", (done) ->
          result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()

    describe 'Performance and stress test scenarios', ->
      performanceTests = [
        {
          description: '默认参数应该使用0.1的阈值',
          city1: 'Toronto',
          city2: 'Toranto',
          expected: true
        },
        {
          description: '零阈值应该要求完全匹配',
          city1: 'Toronto',
          city2: 'Toranto',
          allowDiffRatio: 0,
          expected: false
        },
        {
          description: '100%阈值应该允许任何差异',
          city1: 'Toronto',
          city2: 'Vancouver',
          allowDiffRatio: 1.0,
          expected: true  # 两个不同的词：Toronto/Vancouver，100%阈值应该允许任何编辑距离
        }
      ]

      performanceTests.forEach (test) ->
        testName = "#{test.description} - #{test.city1} vs #{test.city2}"
        if test.allowDiffRatio?
          testName += " (ratio: #{test.allowDiffRatio})"
        it testName, (done) ->
          if test.allowDiffRatio?
            result = cityHelper.isSimilarStrByLD(test.city1, test.city2, test.allowDiffRatio)
          else
            result = cityHelper.isSimilarStrByLD(test.city1, test.city2)
          result.should.equal(test.expected)
          done()
