should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
llmHelper = require '../../built/libapp/llmTranslationHelper'

# ./test.sh -f libapp/llmTranslationHelper.js
describe 'LLM Translation Helper test', ->

  describe 'validateTemplateStructure method tests', ->
    it 'should validate complete template structure successfully', (done) ->
      @timeout(30000)
      
      validTemplate = {
        _id: 'template1'
        nm: 'Universal Translation'
        ver: '1.0'
        status: 'active'
        scenario: 'universal_translation'
        m_cfg: { m_nm: 'gpt' }
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}'
          sys: 'You are a professional translator'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
        ]
        ts: new Date()
        _mt: new Date()
      }

      result = llmHelper.validateTemplateStructure(validTemplate)
      result.should.have.property('valid', true)
      result.should.have.property('errors').which.is.an.Array()
      result.errors.should.have.length(0)
      done()

    it 'should detect missing required fields', (done) ->
      @timeout(30000)
      
      incompleteTemplate = {
        _id: 'template1'
        nm: 'Test Template'
        # Missing required fields: ver, status, scenario, m_cfg, tpl, ts, _mt
      }

      result = llmHelper.validateTemplateStructure(incompleteTemplate)
      result.should.have.property('valid', false)
      result.should.have.property('errors').which.is.an.Array()
      result.errors.length.should.be.above(0)
      result.errors.should.containEql('缺少必填字段: ver')
      result.errors.should.containEql('缺少必填字段: status')
      result.errors.should.containEql('缺少必填字段: scenario')
      done()

    it 'should detect missing model configuration', (done) ->
      @timeout(30000)
      
      templateWithoutModel = {
        _id: 'template1'
        nm: 'Test Template'
        ver: '1.0'
        status: 'active'
        scenario: 'universal_translation'
        m_cfg: {}, # Missing m_nm
        tpl: { main: 'test' }
        ts: new Date()
        _mt: new Date()
      }

      result = llmHelper.validateTemplateStructure(templateWithoutModel)
      result.should.have.property('valid', false)
      result.errors.should.containEql('缺少模型名称: m_cfg.m_nm')
      done()

    it 'should detect unsupported model', (done) ->
      @timeout(30000)
      
      templateWithUnsupportedModel = {
        _id: 'template1'
        nm: 'Test Template'
        ver: '1.0'
        status: 'active'
        scenario: 'universal_translation'
        m_cfg: { m_nm: 'unsupported_model' }
        tpl: { main: 'test' }
        ts: new Date()
        _mt: new Date()
      }

      result = llmHelper.validateTemplateStructure(templateWithUnsupportedModel)
      result.should.have.property('valid', false)
      result.errors.should.containEql('不支持的模型: unsupported_model')
      done()

    it 'should detect missing template content', (done) ->
      @timeout(30000)
      
      templateWithoutMain = {
        _id: 'template1'
        nm: 'Test Template'
        ver: '1.0'
        status: 'active'
        scenario: 'universal_translation'
        m_cfg: { m_nm: 'gpt' }
        tpl: {}, # Missing main
        ts: new Date()
        _mt: new Date()
      }

      result = llmHelper.validateTemplateStructure(templateWithoutMain)
      result.should.have.property('valid', false)
      result.errors.should.containEql('缺少主要模板内容: tpl.main')
      done()

    it 'should validate variable definitions', (done) ->
      @timeout(30000)
      
      templateWithInvalidVars = {
        _id: 'template1'
        nm: 'Test Template'
        ver: '1.0'
        status: 'active'
        scenario: 'universal_translation'
        m_cfg: { m_nm: 'gpt' }
        tpl: { main: 'test' }
        vars: [
          { tp: 'string', req: true }, # Missing nm
          { nm: 'var2', req: true }, # Missing tp
          { nm: 'var3', tp: 'string' } # Missing req
        ]
        ts: new Date()
        _mt: new Date()
      }

      result = llmHelper.validateTemplateStructure(templateWithInvalidVars)
      result.should.have.property('valid', false)
      result.errors.should.containEql('变量 0 缺少名称字段')
      result.errors.should.containEql('变量 1 缺少类型字段')
      result.errors.should.containEql('变量 2 缺少必填标识')
      done()

  describe 'getSortedPromptTemplates method tests', ->
    it 'should sort templates by model priority and version', (done) ->
      @timeout(30000)
      
      templates = [
        {
          _id: 'template1'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'deepseek' }
          ver: '1.0'
          _mt: '2024-01-01'
        },
        {
          _id: 'template2'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '2.0'
          _mt: '2024-01-02'
        },
        {
          _id: 'template3'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-03'
        }
      ]

      result = llmHelper.getSortedPromptTemplates('test content', 'universal_translation', templates)
      result.should.have.length(3)
      # GPT should come first (higher priority), then version 2.0 before 1.0
      result[0]._id.should.equal('template2')
      result[1]._id.should.equal('template3')
      result[2]._id.should.equal('template1')
      done()

    it 'should filter by scenario and status', (done) ->
      @timeout(30000)
      
      templates = [
        {
          _id: 'template1'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-01'
        },
        {
          _id: 'template2'
          status: 'inactive'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-02'
        },
        {
          _id: 'template3'
          status: 'active'
          scenario: 'comment_filter'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-03'
        }
      ]

      result = llmHelper.getSortedPromptTemplates('test content', 'universal_translation', templates)
      result.should.have.length(1)
      result[0]._id.should.equal('template1')
      done()

    it 'should return empty array for no matching templates', (done) ->
      @timeout(30000)
      
      templates = [
        {
          _id: 'template1'
          status: 'active'
          scenario: 'comment_filter'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-01'
        }
      ]

      result = llmHelper.getSortedPromptTemplates('test content', 'universal_translation', templates)
      result.should.have.length(0)
      done()

    it 'should handle empty input', (done) ->
      @timeout(30000)
      
      result = llmHelper.getSortedPromptTemplates('', 'universal_translation', [])
      result.should.have.length(0)
      
      result = llmHelper.getSortedPromptTemplates(null, 'universal_translation', [])
      result.should.have.length(0)
      
      result = llmHelper.getSortedPromptTemplates('test', 'universal_translation', null)
      result.should.have.length(0)
      done()

  describe 'buildTranslationPrompt method tests', ->
    it 'should build prompt successfully with all variables', (done) ->
      @timeout(30000)
      
      template = {
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}. Context: {context}'
          sys: 'You are a professional translator'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
          { nm: 'context', tp: 'string', req: false }
        ]
        m_cfg: { m_nm: 'gpt', temperature: 0.7 }
      }

      variables = {
        text: 'Hello world'
        source_language: 'English'
        target_language: 'Chinese'
        context: 'Real estate listing'
      }

      result = llmHelper.buildTranslationPrompt(template, variables)
      result.should.have.property('success', true)
      result.should.have.property('prompt', 'Translate "Hello world" from English to Chinese. Context: Real estate listing')
      result.should.have.property('systemPrompt', 'You are a professional translator')
      result.should.have.property('modelConfig')
      result.modelConfig.should.deepEqual({ m_nm: 'gpt', temperature: 0.7 })
      done()

    it 'should handle missing optional variables', (done) ->
      @timeout(30000)
      
      template = {
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}. Context: {context}'
          sys: 'You are a professional translator'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
          { nm: 'context', tp: 'string', req: false }
        ]
      }

      variables = {
        text: 'Hello world'
        source_language: 'English'
        target_language: 'Chinese'
        # context is missing but not required
      }

      result = llmHelper.buildTranslationPrompt(template, variables)
      result.should.have.property('success', true)
      result.should.have.property('prompt', 'Translate "Hello world" from English to Chinese. Context: ')
      done()

    it 'should return error for missing required variables', (done) ->
      @timeout(30000)
      
      template = {
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
        ]
      }

      variables = {
        text: 'Hello world'
        source_language: 'English'
        # target_language is missing and required
      }

      result = llmHelper.buildTranslationPrompt(template, variables)
      result.should.have.property('success', false)
      result.should.have.property('error', '缺少必填变量: target_language')
      done()

    it 'should handle empty template', (done) ->
      @timeout(30000)
      
      result = llmHelper.buildTranslationPrompt(null, {})
      result.should.have.property('success', false)
      result.should.have.property('error', '模板不能为空')
      done()

    it 'should handle empty variables', (done) ->
      @timeout(30000)
      
      template = { tpl: { main: 'test' } }
      result = llmHelper.buildTranslationPrompt(template, null)
      result.should.have.property('success', false)
      result.should.have.property('error', '变量不能为空')
      done()

  describe 'parseFilterResult method tests', ->
    it 'should parse pass result correctly', (done) ->
      @timeout(30000)
      
      result = llmHelper.parseFilterResult('PASS: Content is appropriate')
      result.should.have.property('passed', true)
      result.should.have.property('reason', '内容通过审核')
      done()

    it 'should parse reject result with reason', (done) ->
      @timeout(30000)
      
      result = llmHelper.parseFilterResult('REJECT: Contains inappropriate language')
      result.should.have.property('passed', false)
      result.should.have.property('reason', 'Contains inappropriate language')
      done()

    it 'should parse reject result without reason', (done) ->
      @timeout(30000)
      
      result = llmHelper.parseFilterResult('REJECT')
      result.should.have.property('passed', false)
      result.should.have.property('reason', '内容不符合社区规范')
      done()

    it 'should handle case insensitive input', (done) ->
      @timeout(30000)
      
      result = llmHelper.parseFilterResult('pass: content is ok')
      result.should.have.property('passed', true)
      
      result = llmHelper.parseFilterResult('reject: bad content')
      result.should.have.property('passed', false)
      result.should.have.property('reason', 'bad content')
      done()

    it 'should handle unknown format', (done) ->
      @timeout(30000)
      
      result = llmHelper.parseFilterResult('UNKNOWN FORMAT')
      result.should.have.property('passed', false)
      result.should.have.property('reason', '无法解析过滤结果，默认拒绝')
      done()

    it 'should handle empty result', (done) ->
      @timeout(30000)
      
      result = llmHelper.parseFilterResult('')
      result.should.have.property('passed', false)
      result.should.have.property('reason', '过滤结果为空')
      
      result = llmHelper.parseFilterResult(null)
      result.should.have.property('passed', false)
      result.should.have.property('reason', '过滤结果为空')
      done()

  describe 'language validation methods tests', ->
    it 'should validate supported language codes', (done) ->
      @timeout(30000)
      
      llmHelper.isValidLanguageCode('en').should.be.true()
      llmHelper.isValidLanguageCode('zh-cn').should.be.true()
      llmHelper.isValidLanguageCode('zh').should.be.true()
      llmHelper.isValidLanguageCode('kr').should.be.true()
      llmHelper.isValidLanguageCode('English').should.be.true()
      llmHelper.isValidLanguageCode('Chinese').should.be.true()
      done()

    it 'should reject unsupported language codes', (done) ->
      @timeout(30000)
      
      llmHelper.isValidLanguageCode('fr').should.be.false()
      llmHelper.isValidLanguageCode('de').should.be.false()
      llmHelper.isValidLanguageCode('unsupported').should.be.false()
      llmHelper.isValidLanguageCode('').should.be.false()
      llmHelper.isValidLanguageCode(null).should.be.false()
      done()

    it 'should get correct language display names', (done) ->
      @timeout(30000)
      
      llmHelper.getLanguageDisplayName('en').should.equal('English')
      llmHelper.getLanguageDisplayName('zh-cn').should.equal('Chinese')
      llmHelper.getLanguageDisplayName('zh').should.equal('Traditional Chinese')
      llmHelper.getLanguageDisplayName('kr').should.equal('Korean')
      llmHelper.getLanguageDisplayName('unknown').should.equal('unknown')
      done()

  describe 'model mapping methods tests', ->
    it 'should map model names correctly', (done) ->
      @timeout(30000)
      
      llmHelper.getModelName('gpt').should.equal('openAI')
      llmHelper.getModelName('openai').should.equal('openAI')
      llmHelper.getModelName('claude').should.equal('claude')
      llmHelper.getModelName('gemini').should.equal('gemini')
      llmHelper.getModelName('deepseek').should.equal('deepseek')
      llmHelper.getModelName('grok').should.equal('grok')
      llmHelper.getModelName('rm').should.equal('rm')
      llmHelper.getModelName('unknown').should.equal('unknown')
      done()

    it 'should get correct model priorities', (done) ->
      @timeout(30000)
      
      llmHelper.getModelPriority('gpt').should.equal(1)
      llmHelper.getModelPriority('openai').should.equal(1)
      llmHelper.getModelPriority('claude').should.equal(2)
      llmHelper.getModelPriority('gemini').should.equal(3)
      llmHelper.getModelPriority('deepseek').should.equal(4)
      llmHelper.getModelPriority('grok').should.equal(5)
      llmHelper.getModelPriority('rm').should.equal(6)
      llmHelper.getModelPriority('unknown').should.equal(999)
      done()

  describe 'getSortedFilterTemplates method tests', ->
    it 'should sort filter templates correctly', (done) ->
      @timeout(30000)

      templates = [
        {
          _id: 'filter1'
          status: 'active'
          scenario: 'comment_filter'
          m_cfg: { m_nm: 'deepseek' }
          ver: '1.0'
          _mt: '2024-01-01'
        },
        {
          _id: 'filter2'
          status: 'active'
          scenario: 'comment_filter'
          m_cfg: { m_nm: 'gpt' }
          ver: '2.0'
          _mt: '2024-01-02'
        },
        {
          _id: 'filter3'
          status: 'inactive'
          scenario: 'comment_filter'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-03'
        }
      ]

      result = llmHelper.getSortedFilterTemplates('comment_filter', templates)
      result.should.have.length(2) # Only active templates
      result[0]._id.should.equal('filter2') # GPT v2.0 first
      result[1]._id.should.equal('filter1') # DeepSeek second
      done()

    it 'should return empty array for no matching filter templates', (done) ->
      @timeout(30000)

      templates = [
        {
          _id: 'filter1'
          status: 'active'
          scenario: 'image_filter'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-01'
        }
      ]

      result = llmHelper.getSortedFilterTemplates('comment_filter', templates)
      result.should.have.length(0)
      done()

  describe 'buildFilterPrompt method tests', ->
    it 'should build filter prompt successfully', (done) ->
      @timeout(30000)

      template = {
        tpl: {
          main: 'Filter this content: {text}. Context: {context}'
          sys: 'You are a content moderator'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'context', tp: 'string', req: false }
        ]
        m_cfg: { m_nm: 'gpt', temperature: 0.1 }
      }

      variables = {
        text: 'This is a test comment'
        context: 'forum discussion'
      }

      result = llmHelper.buildFilterPrompt(template, variables)
      result.should.have.property('success', true)
      result.should.have.property('prompt', 'Filter this content: This is a test comment. Context: forum discussion')
      result.should.have.property('systemPrompt', 'You are a content moderator')
      result.should.have.property('modelConfig')
      result.modelConfig.should.deepEqual({ m_nm: 'gpt', temperature: 0.1 })
      done()

  describe 'edge cases and boundary tests', ->
    it 'should handle template with missing version in sorting', (done) ->
      @timeout(30000)

      templates = [
        {
          _id: 'template1'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          # ver is missing
          _mt: '2024-01-01'
        },
        {
          _id: 'template2'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-02'
        }
      ]

      result = llmHelper.getSortedPromptTemplates('test content', 'universal_translation', templates)
      result.should.have.length(2)
      # Template with version should come first
      result[0]._id.should.equal('template2')
      result[1]._id.should.equal('template1')
      done()

    it 'should handle template with missing modification time in sorting', (done) ->
      @timeout(30000)

      templates = [
        {
          _id: 'template1'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          # _mt is missing
        },
        {
          _id: 'template2'
          status: 'active'
          scenario: 'universal_translation'
          m_cfg: { m_nm: 'gpt' }
          ver: '1.0'
          _mt: '2024-01-02'
        }
      ]

      result = llmHelper.getSortedPromptTemplates('test content', 'universal_translation', templates)
      result.should.have.length(2)
      # Template with modification time should come first
      result[0]._id.should.equal('template2')
      result[1]._id.should.equal('template1')
      done()

    it 'should handle prompt with multiple variable replacements', (done) ->
      @timeout(30000)

      template = {
        tpl: {
          main: 'Translate "{text}" and "{text}" from {source_language} to {target_language}. The text "{text}" should be translated carefully.'
          sys: 'You are a translator for {target_language}'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
        ]
      }

      variables = {
        text: 'Hello world'
        source_language: 'English'
        target_language: 'Chinese'
      }

      result = llmHelper.buildTranslationPrompt(template, variables)
      result.should.have.property('success', true)
      result.should.have.property('prompt', 'Translate "Hello world" and "Hello world" from English to Chinese. The text "Hello world" should be translated carefully.')
      result.should.have.property('systemPrompt', 'You are a translator for Chinese')
      done()

    it 'should handle empty variable values', (done) ->
      @timeout(30000)

      template = {
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}. Context: {context}'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
          { nm: 'context', tp: 'string', req: false }
        ]
      }

      variables = {
        text: ''
        source_language: 'English'
        target_language: 'Chinese'
        context: ''
      }

      result = llmHelper.buildTranslationPrompt(template, variables)
      result.should.have.property('success', true)
      result.should.have.property('prompt', 'Translate "" from English to Chinese. Context: ')
      done()

    it 'should handle parse filter result with complex reject format', (done) ->
      @timeout(30000)

      complexRejectResult = 'REJECT: This content contains multiple issues: inappropriate language, spam content, and offensive material'
      result = llmHelper.parseFilterResult(complexRejectResult)
      result.should.have.property('passed', false)
      result.should.have.property('reason', 'This content contains multiple issues: inappropriate language, spam content, and offensive material')
      done()

    it 'should handle parse filter result with whitespace', (done) ->
      @timeout(30000)

      resultWithWhitespace = '   PASS: Content is good   '
      result = llmHelper.parseFilterResult(resultWithWhitespace)
      result.should.have.property('passed', true)
      result.should.have.property('reason', '内容通过审核')
      done()

    it 'should handle template without system prompt', (done) ->
      @timeout(30000)

      template = {
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}'
          # sys is missing
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
        ]
      }

      variables = {
        text: 'Hello world'
        source_language: 'English'
        target_language: 'Chinese'
      }

      result = llmHelper.buildTranslationPrompt(template, variables)
      result.should.have.property('success', true)
      result.should.have.property('prompt', 'Translate "Hello world" from English to Chinese')
      result.should.have.property('systemPrompt', '')
      done()
