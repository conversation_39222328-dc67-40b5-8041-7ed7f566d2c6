should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
GeminiTranslator = require '../../built/lib/translator/geminiTranslator'

# ./test.sh -f translator/geminiTranslator.js
describe 'GeminiTranslator translateWithCustomPrompt test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    # 修复构造函数调用，传递正确的参数
    geminiConfig = config.gemini ? {}
    @geminiTranslator = new GeminiTranslator(
      geminiConfig.key ? 'test-api-key',
      'gemini-pro',
      'You are a professional translator',
      geminiConfig.maxUsage ? 10
    )
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      @mockUse = sinon.stub(@geminiTranslator, 'use')
      @mockRelease = sinon.stub(@geminiTranslator, 'release')
      
      # Mock Gemini model
      @mockGenerateContent = sinon.stub()
      @geminiTranslator.modelGemini = { generateContent: @mockGenerateContent }

    afterEach ->
      @mockUse.restore()
      @mockRelease.restore()

    it 'should translate with custom prompt successfully', ->
      @timeout(30000)

      # Mock successful API response
      @mockGenerateContent.resolves({
        response: {
          text: -> 'This is a translated result'
        }
      })

      customPrompt = 'Translate this text: Hello world'
      systemPrompt = 'You are a professional translator'

      @geminiTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
        .then (result) =>
          result.should.equal('This is a translated result')
          @mockGenerateContent.calledOnce.should.be.true()

          # Verify the prompt structure
          fullPrompt = "#{systemPrompt}\n\n#{customPrompt}"
          @mockGenerateContent.firstCall.args[0].should.equal(fullPrompt)

    it 'should work without system prompt', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'Translated without system prompt'
        }
      })

      customPrompt = 'Translate: Hello'

      @geminiTranslator.translateWithCustomPrompt(customPrompt)
        .then (result) =>
          result.should.equal('Translated without system prompt')
          @mockGenerateContent.firstCall.args[0].should.equal(customPrompt)

    it 'should handle API error response', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'Error: API rate limit exceeded'
        }
      })

      @geminiTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('Error: API rate limit exceeded')

    it 'should handle network error', ->
      @timeout(30000)

      @mockGenerateContent.rejects(new Error('Network error'))

      @geminiTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('Network error')

    it 'should handle empty custom prompt', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> ''
        }
      })

      @geminiTranslator.translateWithCustomPrompt('')
        .then (result) ->
          result.should.equal('')

    it 'should handle null custom prompt', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'null handled'
        }
      })

      @geminiTranslator.translateWithCustomPrompt(null)
        .then (result) =>
          result.should.equal('null handled')
          # 验证传递给API的参数 - null会被转换为字符串'null'
          passedPrompt = @mockGenerateContent.firstCall.args[0]
          if passedPrompt?
            passedPrompt.should.equal('null')
          else
            should.not.exist(passedPrompt)

    it 'should remove trailing newlines from result', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'Result with newlines\n\n\n'
        }
      })

      @geminiTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          result.should.equal('Result with newlines')

    it 'should call use() and release() methods', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'Success'
        }
      })

      @geminiTranslator.translateWithCustomPrompt('Test')
        .then (result) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

    it 'should call release() even when error occurs', ->
      @timeout(30000)

      @mockGenerateContent.rejects(new Error('Test error'))

      @geminiTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

  describe 'translateWithCustomPrompt parameter validation', ->
    beforeEach ->
      @mockUse = sinon.stub(@geminiTranslator, 'use')
      @mockRelease = sinon.stub(@geminiTranslator, 'release')
      
      # Mock Gemini model
      @mockGenerateContent = sinon.stub()
      @geminiTranslator.modelGemini = { generateContent: @mockGenerateContent }

    afterEach ->
      @mockUse.restore()
      @mockRelease.restore()

    it 'should handle very long custom prompt', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'Long prompt handled'
        }
      })

      longPrompt = 'A'.repeat(10000)

      @geminiTranslator.translateWithCustomPrompt(longPrompt)
        .then (result) ->
          result.should.equal('Long prompt handled')

    it 'should handle special characters in prompt', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'Special chars handled'
        }
      })

      specialPrompt = 'Test with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''

      @geminiTranslator.translateWithCustomPrompt(specialPrompt)
        .then (result) =>
          result.should.equal('Special chars handled')
          @mockGenerateContent.firstCall.args[0].should.equal(specialPrompt)

    it 'should handle system prompt with special characters', ->
      @timeout(30000)

      @mockGenerateContent.resolves({
        response: {
          text: -> 'System prompt with special chars handled'
        }
      })

      customPrompt = 'Translate this'
      systemPrompt = 'System prompt with 中文 and émojis 🚀'

      @geminiTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
        .then (result) =>
          result.should.equal('System prompt with special chars handled')
          @mockGenerateContent.firstCall.args[0].should.equal("#{systemPrompt}\n\n#{customPrompt}")
