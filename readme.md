# RealMaster Web Application

[![Node.js](https://img.shields.io/badge/Node.js-8.12.0+-green.svg)](https://nodejs.org/)
[![CoffeeScript](https://img.shields.io/badge/CoffeeScript-2.7.0-blue.svg)](https://coffeescript.org/)
[![MongoDB](https://img.shields.io/badge/MongoDB-6.16.0+-orange.svg)](https://www.mongodb.com/)
[![Elasticsearch](https://img.shields.io/badge/Elasticsearch-8.1.0+-yellow.svg)](https://www.elastic.co/)

A comprehensive real estate management web application built with CoffeeScript, Node.js, MongoDB, and Elasticsearch. RealMaster provides a complete solution for real estate professionals, including property management, client relationship management, and data analytics.

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Project Structure](#project-structure)
- [API Documentation](#api-documentation)
- [Contributing](#contributing)
- [License](#license)

## Features

### Core Functionality
- **Property Management**: Complete property listing and management system
- **Client Relationship Management (CRM)**: Lead tracking and customer management
- **Data Import/Export**: Batch processing for MLS data and property information
- **Search & Analytics**: Advanced property search with Elasticsearch integration
- **Email Marketing**: EDM (Electronic Direct Mail) system with templates
- **Mobile Applications**: Native mobile app support
- **Multi-language Support**: Internationalization (i18n) capabilities

### Advanced Features
- **Geocoding & Mapping**: Location-based services and boundary management
- **School Information**: Integration with school databases and rankings
- **Transit Data**: Public transportation information
- **Census Data**: Demographic and statistical information
- **Weather Integration**: Real-time weather data
- **Push Notifications**: Mobile and web notification system
- **Payment Processing**: Stripe integration for transactions
- **AI Integration**: OpenAI and Google AI services

## Tech Stack

### Backend
- **Runtime**: Node.js (>= 8.12.0)
- **Language**: CoffeeScript 2.7.0
- **Database**: MongoDB 6.16.0
- **Search Engine**: Elasticsearch 8.1.0
- **Cache**: Redis
- **Message Queue**: Kafka

### Frontend
- **Framework**: Vue.js 2.6.14
- **Build Tool**: Webpack 4.46.0
- **Styling**: Sass, Less, Stylus
- **UI Components**: Custom components with Vue

### Development Tools
- **Testing**: Mocha, Sinon, Should
- **Code Quality**: CoffeeLint, ESLint
- **Coverage**: NYC (Istanbul)
- **Build Process**: Babel, Webpack

### External Services
- **Cloud Storage**: AWS S3
- **Email**: AWS SES, Nodemailer
- **SMS**: Twilio
- **Push Notifications**: FCM (Firebase Cloud Messaging)
- **Payment**: Stripe
- **AI Services**: OpenAI, Google Generative AI

## Prerequisites

Before running this application, ensure you have the following installed:

- **Node.js**: Version 8.12.0 or higher
- **npm**: Version 1.0.0 or higher
- **MongoDB**: Version 4.0 or higher
- **Elasticsearch**: Version 7.0 or higher
- **Redis**: Version 5.0 or higher

### Optional Dependencies
- **GraphicsMagick**: For image processing
- **PostgreSQL**: For additional data storage
- **MySQL**: For legacy data integration

## Installation

1. **Clone the repository**
   ```bash
   git clone https://bitbucket.org/fredxiang/realmaster-appweb.git
   cd realmaster-appweb
   ```

2. **Install dependencies**
   ```bash
   cd src
   npm install
   ```

3. **Set up environment variables**
   ```bash
   # Edit local.ini with your configuration
   ```

4. **Initialize the database**
   ```bash
   # Follow the setup guide in docs/Dev_enviroment_setup(init)/
   ```

## Configuration

### Environment Variables
You need to have rmconfig
```
<NAME_EMAIL>:real-rm/rmconfig.git
cd rmconfig
vi local.ini
```

### Database Setup

Refer to the following documentation for database setup:
- [MongoDB Setup](docs/Dev_enviroment_setup(init)/01_mongodb.md)
- [Batch Processing Setup](docs/Dev_enviroment_setup(init)/02_runBatches.md)

## Development

### Starting the Development Server

1. **Build the application**
   ```bash
   npm run build:dev
   ```

2. **Start the server**
   ```bash
   wd cfg
   ./start.sh
   ```

3. **Watch for changes**
   ```bash
   # Watch SASS files
   npm run sass:watch:web
   npm run sass:watch:app
   
   # Watch React components
   npm run react:watch
   ```

### Available Scripts

| Script | Description |
|--------|-------------|
| `build:dev` | Build for development with source maps |
| `build` | Build for production (minified) |
| `build:server` | Build server-side code |
| `sass:watch:web` | Watch and compile web SASS files |
| `sass:build:web` | Build web SASS files for production |
| `sass:watch:app` | Watch and compile app SASS files |
| `sass:build:app` | Build app SASS files for production |
| `react:watch` | Watch and compile React components |
| `test` | Run test suite |
| `coverage` | Generate test coverage report |

### Code Quality

The project uses CoffeeLint for code quality. Configuration is in `coffeelint.json`.

```bash
# Run linting
npx coffeelint .
```

## Testing

### Running Tests

```bash
# Run all tests
./start.sh -t unit_testES -e <EMAIL>

# Run tests with coverage
./unitTest/compile.sh -s /home/<USER>/appweb/src
./unitTest/test.sh -m elasticsearch
```

### Test Structure

- **Unit Tests**: Located in `unitTest/` and `unitTestJs/`
- **Integration Tests**: Located in `test/`
- **Coverage Reports**: Generated in `coverage/`

## Deployment

### Production Build

```bash
# Build for production
npm run build

# Start production server
NODE_ENV=production npm start
```

### Docker Deployment
TODO: few packages that requires assembly or gcc not able to run yet
```bash

```

## Project Structure

```
realmaster-appweb/
├── src/                          # Main source code
│   ├── apps/                     # Application modules
│   │   ├── 20_commons/          # Common functionality
│   │   └── 80_sites/            # Site-specific modules
│   │       ├── AppRM/           # Main app controller
│   │       └── WebRM/           # Main web controller
│   ├── apps_common/             # Shared application features
│   ├── batch/                   # Batch processing scripts
│   ├── coffee4client/           # Client-side CoffeeScript
│   ├── lib/                     # Utility libraries
│   ├── model/                   # Data models
│   ├── services/                # External service integrations
│   ├── webroot/                 # Static web assets
│   └── webpack/                 # Webpack configuration
├── docs/                        # Documentation
├── extra/                       # Additional resources
└── fileStorage/                 # File storage
```

### Key Directories

- **`apps/`**: Main application logic and site-specific code
- **`batch/`**: Data import/export and batch processing
- **`coffee4client/`**: Frontend CoffeeScript code
- **`services/`**: External service integrations
- **`docs/`**: Comprehensive documentation

## API Documentation

### Authentication

The application supports multiple authentication methods:
- JWT tokens
- OAuth 2.0
- Apple Sign-In
- OpenID Connect

### Core Endpoints

- **Properties**: `/api/properties`
- **Users**: `/api/users`
- **Leads**: `/api/leads`
- **Schools**: `/api/schools`
- **Transit**: `/api/transit`

### Batch Processing

For batch processing documentation, see:
- [General Batch Processing](docs/Dev_batch/general.md)
- [Data Import/Export](docs/Dev_import/)

## Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
4. **Run tests**
5. **Submit a pull request**

### Code Standards

- Follow CoffeeScript best practices
- Use descriptive variable and function names
- Write comprehensive tests
- Document all public APIs
- Follow the existing code style

### Commit Message Format

Use conventional commit format:

```
type(scope): description

Detailed description of changes
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

## License

This project is proprietary software. All rights reserved.

## Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: See `docs/` directory
- **Issues**: Create an issue in the repository

## Version History

- **v1.5.0**: Current version
- **v1.4.0**: Added AI integration features
- **v1.3.0**: Enhanced batch processing
- **v1.2.0**: Mobile app support
- **v1.1.0**: Elasticsearch integration
- **v1.0.0**: Initial release

---

**RealMaster** - Professional Real Estate Management Solution
