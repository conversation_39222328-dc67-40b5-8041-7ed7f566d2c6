

Q: Error: ENOENT: no such file or directory, open '/home/<USER>/rmconfig/keys/cert.pem'
A: 
```
touch /home/<USER>/rmconfig/keys/cert.pem
touch /home/<USER>/rmconfig/keys/key.pem

```

Q: Error: Cannot find module '/home/<USER>/rmconfig/keys/service-account.json'
A:
```
echo "{}">/home/<USER>/rmconfig/keys/service-account.json
```


MongoServerError[NotPrimaryOrSecondary]: node is not in primary or recovering state
MongoServerError[NotYetInitialized]: no replset config has been received
rs.initiate()
rs.status()