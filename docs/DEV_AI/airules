## 需求文档
- 如果需求文档不完全理解，应该反问用户具体的细节
- 需求文档应该简洁，不应该包括非常具体的解决办法或者数据结构
- 如果一个问题有多个解决方案，那么方案之间的对比应该包括在需求文档里

## 架构设计
- The server is running on dedicated server with nginx
- No cloud services are used or should be for any tasks
- The server OS is rocky linux 9
- The servers shell is /bin/bash.
- Should follow Make it work, make it right, make it fast
- Do not over design unless user specified
- If external lib/modules are used, should check for alternatives if this lib is no longer updated
- Always check memory leaks
- Avoid logging key or tokens


## API设计
- Always validate security related issues, such as user input, user permissions etc.
- Always check if returned json satisfies format {ok:1,msg:'string',err:'err'}
- Always translate fields that are to displayed



## DB/Schema设计
- mongodb is used for most services
- should warn user if there is a potential permformance issue
- always create index when query from db
- always check componded index usage corectness
- always check find result, it could be null
- db field should use short names, eg. nm for name





## 需求文档
如果对需求文档的内容理解不完整，应向用户询问具体细节。
需求文档应简洁明了，不应包含过于具体的解决方案或数据结构。
如果某个问题存在多个解决方案，应在文档中对比各方案的优缺点。

## 架构设计
服务器运行在一台专用服务器上，使用 Nginx。
不应使用任何云服务。
操作系统为 Rocky Linux 9。
默认 shell 为 /bin/bash。
架构应遵循 “先让它运行，再让它正确，最后让它高效”（Make it work, make it right, make it fast）的原则。
除非用户特别指定，否则不应进行过度设计。
如果使用了外部库/模块，应检查其是否仍在维护，并评估替代方案。
始终检查内存泄漏问题。
避免记录日志中包含关键数据或令牌。

## API 设计
始终校验与安全相关的问题，例如用户输入、用户权限等。
始终确保返回的 JSON 格式为 { ok: 1, msg: 'string', err: 'err' }。
所有用于展示的字段应进行适当翻译或映射。

## 数据库/Schema 设计
大多数服务使用 MongoDB。
如果存在潜在的性能问题，应向用户发出警告。
每当对数据库进行查询时，应创建索引。
始终检查复合索引是否使用正确。
始终检查 find 查询的结果是否为空。
数据库字段应使用简短命名，例如 nm 代表 name。

## 文档与注释
项目根目录需包含 README，说明项目背景、环境要求、启动方式。
每个模块应有简要的设计说明文档或 UML 图。
API 接口文档使用 Swagger/OpenAPI 规范维护，并保持与代码同步。
在变更设计或新增功能时，及时更新相应文档。

## 性能优化
对热点代码或数据库慢查询进行剖析，并优化或加缓存。
使用连接池管理数据库/服务连接，避免频繁创建销毁。
在内存和磁盘之间权衡，合理使用内存缓存（如 Redis）。
性能优化应在功能正确后再进行，避免过早优化。

## 测试与质量保证
单元测试覆盖率应 ≥ 80%。
核心路径必须编写集成测试或端到端测试。
引入静态代码分析工具（如 golangci-lint）并集成到 CI 流程。
每次合并前需通过自动化测试，并由至少一名同事 Code Review。
对外部 API 接口使用 Mock 或契约测试，保证稳定性。


## 错误处理
API 层统一捕获异常并转换为标准返回格式。
对外部依赖（网络、数据库、第三方服务）调用加超时和重试机制。
对预期内错误使用业务级错误码，并在文档中列出常见错误码及含义。
不要吞掉异常，关键错误应记录堆栈并上报。
在用户可见层面给出可操作的提示，而不是内部堆栈信息。

## 日志与监控
日志级别分为 DEBUG/INFO/WARN/ERROR，生产环境默认只保留 INFO 及以上。
日志中不得出现敏感字段（密码、Token、PII）。
关键业务操作和错误应打埋点，配合监控工具（如 Prometheus + Grafana）。
定义重要指标（请求延迟、错误率、QPS）并设置告警阈值。
定期回顾监控数据，优化系统性能或容量规划。

## 代码风格与规范
遵循统一的编码规范（如 Go 官方 gofmt、golint）。
函数和变量命名尽量简洁明了，使用驼峰或下划线风格保持一致。
每个函数/模块在超过 50 行时，应考虑拆分。
每个文件不应超出1000行
文件内部应按：导入包 → 常量 → 变量 → 类型定义 → 函数 顺序组织。
保持注释有效且及时更新，关键逻辑处应有必要说明。