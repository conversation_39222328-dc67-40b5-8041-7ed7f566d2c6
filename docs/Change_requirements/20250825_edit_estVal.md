# 需求 [edit_estVal]

我们增加一个手动估价调整的设置吧。在prop management页面。需要propAdmin permission。保存在estimate那个collection中，需要输入3个值，不会被自动程序覆盖，优先于自动程序值的显示。对于admin，显示一下是手动调整的，谁调整的。也加一个隐藏估价的flag，同一个collection。隐藏时，对admin显示一下是谁隐藏的。

## 需求提出人:   Fred 

## 修改人：      ZhangMan

## 提出日期:      2025-08-24

## 解决办法

1. 在Edit Prop Management页面中增加估价值修改：result,min,max。只有 propAdmin 可以修改值。将这三个值保存在 prop_estimate 表中，分别取名：sp_man_result，sp_man_max，sp_man_min.
2. 在估价展示页面显示时，如果使用的是手动修改的值，则给 admin 一个提示当前值是手动调整的，谁调整的信息。
3. 将之前隐藏估价的flag从properties调整到 prop_estimate 表中。隐藏时对admin显示一下隐藏提示。
4. 考虑到可能不需要一次性修改3个值，因此增加灵活性，admin没有编辑的值保存时使用当前原始的估价数据。
5. 之前的隐藏估价是非房大师经纪和admin不能看，还是保留这个显示机制，加一行小字提示该估价已经隐藏/修改。

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-08-26

## online-step

1. 重启项目
