# 需求 [fix_check_cityTrans]

## 反馈

1. Fred反馈appweb log里面有不少下面这样log
```
shouldTransCity: city 'Broker Reciprocity' not found in cityCache
shouldTransCity: city 'Electricity' not found in cityCache
shouldTransCity: city 'Broker Reciprocity' not found in cityCache
```

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-09-05

## 原因

1. 在i18n翻译时,添加了对`ctx`是`city`的正则表达式判断，导致在翻译`Building.BrokerReciprocity`和`Building.Electricity`的key时,判断成为city的翻译导致出现warning log

## 解决办法

1. i18n翻译时,取消`ctx`对`city`的正则表达式判断,直接使用`ctx.toLowerCase() is 'city'`进行判断

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-09-05

## online-step

1. 重启server