# 上层表数据说明 （“上层表”是做数据统计从而获取某些特征的正确值，比如 Build year。用aggregate结尾的表）

### amenities_aggregate
> 从第三方获取的includedInMaintenanceFees，amenities数据，见https://github.com/real-rm/rm3rdparty，src/batch/prop/condosSqft.coffee

### boundary
> 第三方 statcan导入的数据，见docs/Dev_boundary/importBoundary.md

### census
> 第三方 2016 2021 数据

### cmty_crime_rate
> src/apps/80_sites/WebRM/inputCriminal.coffee

### cmty_image
> 区图片db

### cmtyFromProperties
> aggregate 的cmty数据

### condos
> aggregate, amen_extra,mfee_inc, src/batch/condos/condos.coffee 生成，后端http://www.test:8080/buildings 管理

### data.condo_complex
> 手动关联condo地址


### floorplans
> 从condos获取的数据，图片加水印，用户可以修改这里面数据

### geo_cache
> 从第三方服务获取的经纬度信息

### properties_condo
> aggregate, latlng, src/batch/prop/refineGeocoding.coffee


### property_boundary
> 第三方数据，每个房源范围 docs/Dev_boundary/importBoundary.md

### prov_city_cmty
> aggregate, 省市社区对应关系

### sqfts_aggregate
> aggregate, sqft, level, bltyr

### stat_tag
> stat_uname tag, src/batch/tag/addTagToStatUname.coffee

### stat_type 
> ?

### stat_uname
> ? 来源不明，社区经纬度和统计的房源信息

### treb_comm2
> 第三方 docs/Dev_boundary/importBoundary.md




行政关系: Region&CityTown&RmCmty&Cmty
Building附属信息: Census/Cmty/SchoolBoard/School
经纪资料: Brokerage/Agent

